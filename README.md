# vtott-mobile-preview-app

VTOTT Preview app for mobile (Android &amp; IOS)

# To resolve errors due to react-native-orientation module,

## after executing `npm install`, add these necessary modifications to the following package

```
vtott-mobile-preview-app/node_modules/react-native-orientation/android/build.gradle
```

```
compileSdkVersion rootProject.hasProperty('compileSdkVersion') ? rootProject.compileSdkVersion : 23
buildToolsVersion rootProject.hasProperty('buildToolsVersion') ? rootProject.buildToolsVersion : "23.0.1"

...
defaultConfig {
    minSdkVersion rootProject.hasProperty('minSdkVersion') ? rootProject.minSdkVersion : 16
    targetSdkVersion rootProject.hasProperty('targetSdkVersion') ? rootProject.targetSdkVersion : 22
    ...
}

implementation "com.facebook.react:react-native:+"
...
```

replace the following line in

```
OrientationModule.java
```

```
- activity.registerReceiver(receiver, new IntentFilter("onConfigurationChanged"));
+ activity.registerReceiver(receiver, new IntentFilter("onConfigurationChanged"), Context.RECEIVER_NOT_EXPORTED);
```

# run app with environments

## For Development:

```
ENVFILE=.env.development npx react-native run-android
ENVFILE=.env.development npx react-native run-ios
```

## For Production:

```
ENVFILE=.env.production npx react-native run-android
ENVFILE=.env.production npx react-native run-ios
```

## iOS

use "react-native-webview": "13.12.5" for ios and "react-native-webview": "13.13.2" for android
