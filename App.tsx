import { StatusBar, View, ActivityIndicator, LogBox } from "react-native";
import React, { useEffect, useState } from "react";
import RootStack from "./src/navigator/RootStack";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { store } from "./src/redux/store";
import { AppService } from "./src/services/AppService";
import ErrorScreen from "./src/screens/ErrorScreen";
import { AsyncStorageHandler } from "./src/services/AsyncStorageHandler";
import Config from "react-native-config";
import BootSplash from "react-native-bootsplash";

const App: React.FC = () => {
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const initializeApp = async () => {
    try {
      setHasError(false);
      setIsLoading(true);

      const { dispatch } = store;

      // identify the env and redirect navigation
      // to appid auth screen(dev env) or home screen(prod env with default app id)
      const isDevEnv = Config.SHOW_APP_ID_INPUT;
      if (isDevEnv == 'false') {
        // prod env
        if (Config.APP_ID) {
          await AsyncStorageHandler.setAppId(Config.APP_ID, dispatch);
        }
      } else {
        await AsyncStorageHandler.loadAppId(dispatch);
      }
      LogBox.ignoreAllLogs(); // Hides al

      if (store.getState().authScreen.appId) {
        await AppService.initApp(dispatch);
      } else {
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Error initializing app:", error);
      setHasError(true);
    } finally {

      setIsLoading(false);
    }
  };

  useEffect(() => {
    initializeApp();
    setTimeout(async () => {
      await BootSplash.hide({ fade: true });
      console.log("BootSplash has been hidden successfully");
    }, 3000);
  }, []);


  return (
    <SafeAreaProvider>
      <StatusBar barStyle="dark-content" />

      {isLoading ? (
        <View
          style={{ flex: 1, justifyContent: "center", alignItems: "center", backgroundColor: 'white' }}
        >
          <ActivityIndicator size="large" color="#000000" />
        </View>
      ) : hasError ? (
        <ErrorScreen onRetry={initializeApp} />
      ) : (
        <RootStack />
      )
      }
    </SafeAreaProvider >
  );
};

export default App;
