buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 34
        ndkVersion = "27.1.12297006"
        kotlinVersion = "1.9.24"
        useExoplayerIMA = true

    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
    }
}

apply plugin: "com.facebook.react.rootproject"

allprojects {
    repositories {
        maven{
            url 'https://mvn.jwplayer.com/content/repositories/releases/'
        }
    }
}

configurations.configureEach {
    exclude group: 'com.google.ads.interactivemedia', module: 'interactivemedia'
}
