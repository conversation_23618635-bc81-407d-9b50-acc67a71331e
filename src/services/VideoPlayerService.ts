// includes methods to fetch video details

import axios from "axios";
import { store } from "../redux/store";

export class VideoPlayerService {
  static videoInfo = store.getState().homeScreen.videoCloud;

  /// fetch youtube playlist
  static fetchYoutubePlaylist = async (
    playlistId: string
  ): Promise<unknown[]> => {
    try {
      const response = await axios.get(
        `https://www.googleapis.com/youtube/v3/playlistItems`,
        {
          params: {
            part: "snippet",
            playlistId: playlistId,
            maxResults: 50,
            key: VideoPlayerService.videoInfo[0].cloudKey,
          },
        }
      );

      const videoData = response.data.items.map((item) => {
        console.log("item.snippet.thumbnails------->", item.snippet.thumbnails);
        const thumbnail =
          item.snippet.thumbnails.maxres?.url ??
          item.snippet.thumbnails.high?.url ??
          item.snippet.thumbnails.default?.url ??
          item.snippet.thumbnails.standard?.url ??
          item.snippet.thumbnails.medium?.url;
        console.log(
          "videoData item description------->",
          item.snippet.description
        );
        console.log("videoData thumbnail------->", thumbnail);

        return {
          title: item.snippet.title,
          description: item.snippet.description,
          videoId: item.snippet.resourceId.videoId,
          thumbnail: thumbnail,
          etag: item.etag,
          videoOwnerChannelTitle: item.snippet.videoOwnerChannelTitle,
          videoOwnerChannelId: item.snippet.videoOwnerChannelId,
        };
      });

      return videoData;
    } catch (error) {
      console.error("Error fetching playlist:", error);
      throw error;
    }
  };

  ///fetch jwplayer playlist
  static fetchJwplayerPlaylist = async (
    playlistId: string
  ): Promise<unknown[]> => {
    const url = "https://cdn.jwplayer.com/v2/playlists/" + playlistId;
    console.log("----------getdata--------", url);

    try {
      const result = await axios.get(url);
      console.log("jwplayer result------->", result.data);
      return result.data;
    } catch (e) {
      console.log(e);
      throw e;
    }
  };
}
