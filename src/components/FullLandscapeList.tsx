import React, { useEffect, useState } from 'react';
import {
  FlatList,
  Text,
  Dimensions,
  View,
  StyleSheet,
  TouchableOpacity,
  Linking,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { VideoPlayerService } from '../services/VideoPlayerService';
import { Image } from 'react-native-elements';

const { width } = Dimensions.get('window');

interface Item {
  itemid?: {
    landscapeThumbnail?: { imagekey?: string };
    pageType?: string;
    externalPage?: string;
    urlName?: string;
    tag?: string;
    mainHeader?: string;
    devices?: { itemName?: string; itemVisibility?: boolean }[];
  };
}

interface JWItem {
  image: string;
  MediaType?: string;
  title?: string;
}

interface Props {
  datas: {
    playlistId?: string;
    items?: Item[];
  };
  listtitle: string;
  color: string;
  appsettings?: {
    imageBorder?: boolean;
  };
}

const FullLandscapeList: React.FC<Props> = ({ datas, listtitle, color, appsettings }) => {
  const navigation = useNavigation();
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);
  const videoInfo = useSelector((state: RootState) => state.homeScreen.videoCloud);
  const themeColor = useSelector((state: RootState) => state.homeScreen.colors);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const [JwData, setJwData] = useState<{ playlist?: JWItem[] }>({});
  const [youtubeData, setYoutubeData] = useState<unknown[]>([]);
  const [isYoutubeVideo, setIsYoutubeVideo] = useState(false);
  const [isDataLoading, setIsDataLoading] = useState<boolean>(false);
  const [FullData, setFullData] = useState<
    {
      imageUrl: string;
      data: unknown;
      pageType: string;
      itemnavigation: string;
      urlname: string;
      tag?: string;
      mainHeader?: string;
    }[]
  >([]);


  useEffect(() => {
    initVideoInfo();
  }, [datas.playlistId]);

  const initVideoInfo = async () => {
    try {
      if (!datas.playlistId) {
        return;
      }
      setIsDataLoading(true)

      if (videoInfo[0].cloudName === 'YouTube') {
        const videoData = await VideoPlayerService.fetchYoutubePlaylist(datas.playlistId);
        setYoutubeData(videoData);
        setIsYoutubeVideo(true);
      } else {
        const videoData = await VideoPlayerService.fetchJwplayerPlaylist(datas.playlistId) as JWItem[];
        setJwData(videoData);
      }
      setIsDataLoading(false)
    } catch (error) {
      console.error('Error fetching playlist [FullLandscapeList]:', error);
    }
  }

  useEffect(() => {
    const updatedData: typeof FullData = [];

    if (datas.items) {
      datas.items.forEach((item) => {
        updatedData.push({
          imageUrl: imageUrl + (item.itemid?.landscapeThumbnail?.imagekey ?? ''),
          data: item,
          pageType: item.itemid?.pageType ?? '',
          itemnavigation: item.itemid?.externalPage ?? '',
          urlname: item.itemid?.urlName ?? '',
          tag: item.itemid?.tag,
          mainHeader: item.itemid?.mainHeader,
        });
      });
    }

    if (JwData.playlist) {
      JwData.playlist.forEach((item) => {
        updatedData.push({
          imageUrl: item.image,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          tag: item.MediaType,
          mainHeader: item.title,
        });
      });
    }

    if (youtubeData != null) {
      youtubeData.forEach((item, key) => {
        updatedData.push({
          imageUrl: item.thumbnail,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          tag: item.etag,
          mainHeader: item.title,
        });
      });
    }

    setFullData(updatedData);
  }, [datas.items, JwData, youtubeData]);

  const handleNavigationTo = (
    pageType: string,
    itemnavigation: string,
    itemdata: Item | JWItem,
    urlname: string
  ) => {
    switch (pageType) {
      case 'externalPage':
        return Linking.openURL(itemnavigation);
      case 'articlePage':
        return navigation.navigate('ArticleDetails', { item: itemdata, color });
      case 'seasonPage':
        return navigation.navigate('Season', { item: itemdata, color });
      case 'videoPage':
        return navigation.navigate('Details', { item: itemdata, color });
      case 'normalPage':
        return navigation.navigate('MoreDetail', { itemurlname: urlname, color });
      default:
        return;
    }
  };

  const renderLoader = () => {
    return isDataLoading ? (
      <View style={styles(isRTL).loaderStyle}>
        <ActivityIndicator size="small" color="#aaa" />
      </View>
    ) : null;
  };



  return isDataLoading ?
    (
      <View style={[styles(isRTL).loaderStyle, { justifyContent: 'center', flex: 1 }]}>
        <ActivityIndicator size='large' color="#aaa" />
      </View>
    ) : (
      <View style={{ marginBottom: width / 25, marginTop: width / 60 }}>
        <View style={styles(isRTL).row} />
        <View style={styles(isRTL).inputWrap}>
          <Text style={[styles(isRTL).text1, { color: themeColor.primaryTextColor }]}>{listtitle}</Text>
        </View>
        <View style={{ marginBottom: width / 45, alignItems: isRTL ? 'flex-end' : 'flex-start' }}>
          <FlatList
            style={{ marginTop: width / 50, direction: isRTL ? 'rtl' : 'ltr' }}
            numColumns={2}
            scrollEnabled={false}
            showsHorizontalScrollIndicator={false}
            data={FullData}
            ListFooterComponent={renderLoader}

            renderItem={({ item }) => (
              <TouchableOpacity
                onPress={() => {
                  if (item.data) {
                    handleNavigationTo(item.pageType, item.itemnavigation, item.data, item.urlname);
                  }
                  // if ( item.data &&
                  //   'itemid' in item.data &&
                  //   item.data.itemid?.devices?.[2]?.itemName === 'iOS' &&
                  //   item.data.itemid?.devices?.[2]?.itemVisibility === true
                  // ) { } else {
                  //   navigation.navigate('pagenotfound', { color });
                  // }
                }}
              >
                <Image
                  PlaceholderContent={<ActivityIndicator />}
                  source={{ uri: item.imageUrl || undefined }}
                  style={{
                    width: width / 2.1,
                    height: width / 3.6,
                    aspectRatio: 16 / 9,
                    borderRadius: appsettings?.imageBorder === false ? width / 50 : 0,
                    marginRight: width / 50,
                    padding: 8
                  }}
                  resizeMode='contain'
                />
              </TouchableOpacity>
            )}
          />
        </View>
      </View >
    );
};

export default FullLandscapeList;

const styles = (isRTL: boolean) => StyleSheet.create({
  row: {
    flex: 1,
    justifyContent: 'space-around',
    direction: isRTL ? 'rtl' : 'ltr',
  },
  inputWrap: {
    flex: 1,
    alignSelf: isRTL ? 'flex-end' : 'flex-start',
    direction: isRTL ? 'rtl' : 'ltr',

  },
  text1: {
    fontSize: width / 25,
    fontWeight: 'bold',
    marginHorizontal: width / 40,
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
  },
  loaderStyle: {
    marginVertical: 16,
    alignItems: "center",
  },
});
