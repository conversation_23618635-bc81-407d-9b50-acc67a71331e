import React, { useState, useEffect } from 'react';
import {
  FlatList,
  Text,
  Dimensions,
  View,
  StyleSheet,
  TouchableOpacity,
  Linking,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { VideoPlayerService } from '../services/VideoPlayerService';
import { Image } from 'react-native-elements';

const { width } = Dimensions.get('window');

// Define types for the props
interface LandscapeListProps {
  datas: any; // Change 'any' to a more specific type if needed
  playlistId: string;
  listtitle: string;
  seemoretextvis: boolean;
  seemoretext: string;
  seemorecomp: React.ComponentType<any>;
  color: string;
  appsettings?: {
    imageBorder?: boolean;
  };
}

interface ItemData {
  itemid: {
    landscapeThumbnail: {
      imagekey: string;
    };
    pageType: string;
    externalPage: string;
    urlName: string;
    displayName: string;
    introduction: string;
    devices: { itemName: string; itemVisibility: boolean }[];
  };
}

interface FullDataType {
  imageUrl: string;
  data: ItemData;
  pageType: string;
  itemnavigation: string;
  urlname: string;
  displayname: string;
  introduction: string;
}

const LandscapeList: React.FC<LandscapeListProps> = (props) => {
  const navigation = useNavigation();
  const colors = useSelector((state: RootState) => state.homeScreen.colors);
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);
  const videoInfo = useSelector((state: RootState) => state.homeScreen.videoCloud);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const [JwData, setJwData] = useState<unknown[]>([]);
  const [youtubeData, setYoutubeData] = useState<unknown[]>([]);
  const [isYoutubeVideo, setIsYoutubeVideo] = useState(false);
  const playlistInfo: FullDataType[] = [];

  useEffect(() => {
    initVideoInfo();
  }, [JwData, youtubeData]);

  const initVideoInfo = async () => {
    try {
      if (props.datas.playlistId === null) {
        return;
      }
      if (videoInfo[0].cloudName === 'YouTube') {
        const videoData = await VideoPlayerService.fetchYoutubePlaylist(props.datas.playlistId);
        setYoutubeData(videoData);
        setIsYoutubeVideo(true);
      } else {
        const videoData = await VideoPlayerService.fetchJwplayerPlaylist(props.datas.playlistId);
        setJwData(videoData);
      }
    } catch (error) {
      console.error('Error fetching playlist [LandscapeList]:', error);
    }
  }


  function setPlaylistData() {
    if (props.datas != null) {
      props.datas.items.forEach((item, key) => {
        playlistInfo.push({
          imageUrl: imageUrl + (item.itemid.landscapeThumbnail != null ? item.itemid.landscapeThumbnail.imagekey : ""),
          data: item,
          pageType: item.itemid.pageType,
          itemnavigation: item.itemid.externalPage,
          urlname: item.itemid.urlName,
          tag: item.itemid.tag,
          mainHeader: item.itemid.mainHeader,
        });
      });
    }

    if (JwData.playlist != null) {
      JwData.playlist.forEach((item, key) => {
        playlistInfo.push({
          imageUrl: isYoutubeVideo ? item.thumbnail : item.image,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          tag: item.MediaType,
          mainHeader: item.title,
        });
      });
    }

    if (youtubeData != null) {
      youtubeData.forEach((item, key) => {
        playlistInfo.push({
          imageUrl: item.thumbnail,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          tag: item.etag,
          mainHeader: item.title,
        });
      });
    }
  }


  function handleNavigationTo(
    pageType: string,
    itemnavigation: string,
    itemdata: any,
    urlname: string
  ) {
    if (pageType === 'externalPage') {
      return Linking.openURL(itemnavigation);
    } else if (pageType === 'articlePage') {
      return navigation.push('ArticleDetails', {
        item: itemdata,
        color: props.color,
      });
    } else if (pageType === 'seasonPage') {
      return navigation.push('Season', {
        item: itemdata,
        color: props.color,
      });
    } else if (pageType === 'videoPage') {
      if (isYoutubeVideo) {
        navigation.push('YouTubeDetailsScreen', { item: itemdata, color: props.color, });
      } else {
        navigation.push('Details', { item: itemdata, color: props.color, });
      }
    } else if (pageType === 'normalPage') {
      return navigation.push('MoreDetail', {
        itemurlname: urlname,
        color: props.color,
      });
    }
  }

  const handleItemPress = (item: any) => {
    if (item) {
      handleNavigationTo(item.pageType, item.itemnavigation, item.data, item.urlname);
    }
    // if (item.data.itemid && item.data.itemid.devices[2]?.itemName === 'iOS') { } else {
    //   navigation.push('pagenotfound', { color: props.color });
    // }
  };

  return (
    setPlaylistData(),
    (
      <View style={{ marginBottom: width / 25, marginTop: width / 60 }}>
        <View style={styles(colors, isRTL).row}>
          <View style={styles(colors, isRTL).inputWrap}>
            <Text style={styles(colors, isRTL).text1}>{props.listtitle}</Text>
          </View>
          {props.seemoretextvis && props.seemoretext && (
            <View style={styles(colors, isRTL).inputWrap}>

              <TouchableOpacity
                style={styles(colors, isRTL).text2}
                onPress={() => {
                  navigation.navigate('SeeMore', {
                    itemId: props.listtitle,
                    listdatas: props.datas,
                    comp: props.seemorecomp,
                    color: props.color,
                  });
                }}>
                <Text style={styles(colors, isRTL).text2}>{props.seemoretext}</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
        <View style={{ marginBottom: width / 45, alignItems: isRTL ? 'flex-end' : 'flex-start' }}>

          <FlatList
            style={{ marginTop: width / 50 }}
            horizontal
            showsHorizontalScrollIndicator={false}
            data={playlistInfo}
            renderItem={({ item }) => (
              <TouchableOpacity
                onPress={() => handleItemPress(item)}>
                {item.imageUrl != null && (
                  <Image
                    PlaceholderContent={<ActivityIndicator />}
                    source={{
                      uri: item.imageUrl,
                    }}
                    resizeMode='stretch'
                    style={{
                      width: width / 2,
                      height: width / 4,
                      borderWidth: 0,
                      borderColor: '#d35647',
                      borderRadius: props.appsettings?.imageBorder === false ? width / 50 : 0,
                      margin: width / 150,
                      marginBottom: width / 50,

                    }}
                  />
                )}
              </TouchableOpacity>
            )}
          />
        </View>
      </View>
    )
  );
};

export default LandscapeList;


const styles = (colors, isRTL) => StyleSheet.create({
  row: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: isRTL ? 'flex-end' : 'flex-start',
    direction: isRTL ? 'rtl' : 'ltr',
  },
  inputWrap: {
    flex: 1,
    borderColor: colors.primaryTextColor,
    alignSelf: isRTL ? 'flex-end' : 'flex-start',
    direction: isRTL ? 'rtl' : 'ltr',
    justifyContent: isRTL ? 'flex-end' : 'flex-start',
    alignItems: isRTL ? 'flex-end' : 'flex-start',

  },
  text1: {
    color: colors.primaryTextColor,
    fontSize: width / 30,
    fontWeight: 'bold',
    marginHorizontal: width / 40,
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
  },
  text2: {
    color: colors.primaryTextColor,
    fontSize: width / 30,
    fontWeight: 'bold',
    marginHorizontal: width / 40,
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
  },
});