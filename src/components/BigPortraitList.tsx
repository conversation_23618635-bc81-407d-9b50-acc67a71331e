import React, { useState, useEffect } from 'react';
import {
  FlatList,
  Text,
  Dimensions,
  View,
  StyleSheet,
  TouchableOpacity,
  Linking,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { VideoPlayerService } from '../services/VideoPlayerService';

const { width } = Dimensions.get('window');

// Define the structure of item data
interface ItemId {
  portraitThumbnail?: { imagekey: string };
  pageType?: string;
  externalPage?: string;
  urlName?: string;
  devices?: { itemName?: string; itemVisibility?: boolean }[];
}

interface Item {
  itemid: ItemId;
}

export interface JwPlaylistItem {
  image?: string;
  PortraitImage?: string;
  title?: string;
  description?: string;
}

// Define props for BigPortraitList
export interface BigPortraitListProps {
  datas: {
    items: Item[];
    playlistId?: string;
  };
  listtitle: string;
  seemoretextvis?: boolean;
  seemoretext?: string;
  seemorecomp?: string;
  color?: string;
  appsettings?: {
    imageBorder?: boolean;
  };
}

const BigPortraitList: React.FC<BigPortraitListProps> = (props) => {
  const navigation = useNavigation();
  const colors = useSelector((state: RootState) => state.homeScreen.colors);
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);
  const videoInfo = useSelector((state: RootState) => state.homeScreen.videoCloud);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const [jwData, setJwData] = useState<{ playlist?: JwPlaylistItem[] }>({});
  const [youtubeData, setYoutubeData] = useState<unknown[]>([]);
  const [isYoutubeVideo, setIsYoutubeVideo] = useState(false);
  const FullData: {
    imageUrl: string;
    data: unknown;
    pageType: string;
    itemnavigation: string;
    urlname: string;
  }[] = [];

  useEffect(() => {
    initVideoInfo();
  }, [jwData, youtubeData]);

  const initVideoInfo = async () => {
    try {
      if (!props.datas.playlistId) {
        return;
      }

      if (videoInfo[0].cloudName === 'YouTube') {
        const videoData = await VideoPlayerService.fetchYoutubePlaylist(props.datas.playlistId);
        setYoutubeData(videoData);
        setIsYoutubeVideo(true);
      } else {
        const videoData = await VideoPlayerService.fetchJwplayerPlaylist(props.datas.playlistId) as JwPlaylistItem[];
        setJwData(videoData);

      }
    } catch (error) {
      console.error('Error fetching playlist [BigPortraitList]:', error);
    }
  }

  function setPlaylistData() {
    if (props.datas?.items) {
      props.datas.items.forEach((item) => {
        FullData.push({
          imageUrl: imageUrl + (item.itemid?.portraitThumbnail?.imagekey || ''),
          data: item,
          pageType: item.itemid?.pageType || '',
          itemnavigation: item.itemid?.externalPage || '',
          urlname: item.itemid?.urlName || '',
        });
      });
    }

    if (jwData.playlist) {
      jwData.playlist.forEach((item) => {
        FullData.push({
          imageUrl: item.PortraitImage || item.image || '',
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
        });
      });
    }

    if (youtubeData != null) {
      youtubeData.forEach((item, key) => {
        FullData.push({
          imageUrl: item.thumbnail,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
        });
      });
    }
  }
  const handleNavigationTo = (
    pageType: string,
    itemnavigation: string,
    itemdata: Item | JwPlaylistItem,
    urlname: string
  ) => {
    if (pageType === 'externalPage') {
      Linking.openURL(itemnavigation);
    } else if (pageType === 'articlePage') {
      navigation.push('ArticleDetails', { item: itemdata, color: props.color });
    } else if (pageType === 'seasonPage') {
      navigation.push('Season', { item: itemdata, color: props.color });
    } else if (pageType === 'videoPage') {
      if (isYoutubeVideo) {
        navigation.push('YouTubeDetailsScreen', { item: itemdata, color: props.color, });
      } else {
        navigation.push('Details', { item: itemdata, color: props.color, });
      }
    } else if (pageType === 'normalPage') {
      navigation.push('MoreDetail', { itemurlname: urlname, color: props.color });
    }
  };

  return (
    setPlaylistData(),
    (
      <View style={{ marginBottom: width / 25, marginTop: width / 60 }}>
        <View style={styles(colors, isRTL).row}>
          <View style={styles(colors, isRTL).inputWrap}>
            <Text style={styles(colors, isRTL).text1}>{props.listtitle}</Text>
          </View>
          <View style={styles(colors, isRTL).inputWrap}>
            {props.seemoretextvis && props.seemoretext && (
              <TouchableOpacity
                style={styles(colors, isRTL).text2}
                onPress={() => {
                  navigation.navigate('SeeMore', {
                    itemId: props.listtitle,
                    listdatas: props.datas,
                    comp: props.seemorecomp,
                    color: props.color,
                  });
                }}>
                <Text style={styles(colors, isRTL).text2}>{props.seemoretext}</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        <FlatList
          style={{ marginTop: width / 50 }}
          horizontal
          showsHorizontalScrollIndicator={false}
          data={FullData}
          keyExtractor={(item, index) => index.toString()}
          renderItem={({ item }) => (
            <TouchableOpacity
              onPress={() => {
                if (item.data) {
                  handleNavigationTo(item.pageType, item.itemnavigation, item.data, item.urlname);
                }
                // if (
                //   item.data &&
                //   'itemid' in item.data &&
                //   item.data.itemid?.devices?.[2]?.itemName === 'iOS' &&
                //   item.data.itemid.devices[2]?.itemVisibility
                // ) { } else {
                //   navigation.push('pagenotfound', { color: props.color });
                // }
              }}>
              {item.imageUrl ? (
                <Image
                  source={{ uri: item.imageUrl }}
                  style={{
                    width: width / 2.06,
                    height: width / 1.4,
                    borderWidth: 0,
                    borderColor: '#d35647',
                    borderRadius: props.appsettings?.imageBorder === false ? width / 50 : 0,
                    margin: width / 150,
                    marginBottom: width / 50,
                  }}
                />
              ) : null}
            </TouchableOpacity>
          )}
        />
      </View>
    )
  );
};

export default BigPortraitList;


const styles = (colors, isRTL: boolean) => StyleSheet.create({
  row: {
    flex: 1,
    flexDirection: 'row',
  },
  inputWrap: {
    flex: 1,
    borderColor: colors.primaryTextColor,
  },
  text1: {
    color: colors.primaryTextColor,
    fontSize: width / 30,
    fontWeight: 'bold',
    marginHorizontal: width / 40,
  },
  text2: {
    color: colors.primaryTextColor,
    fontSize: width / 30,
    fontWeight: 'bold',
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
    marginHorizontal: width / 40,
  },
});