import React, { useEffect, useState } from 'react';
import {
  FlatList,
  Text,
  Dimensions,
  View,
  StyleSheet,
  TouchableOpacity,
  Linking,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { VideoPlayerService } from '../services/VideoPlayerService';
import { Image } from 'react-native-elements';

// Define types for the props
interface NewArticleFullImageProps {
  datas: {
    playlistId: string;
    items: Array<{
      itemid: {
        landscapeThumbnail: {
          imagekey: string;
        };
        pageType: string;
        externalPage: string;
        urlName: string;
        displayName: string;
        introduction: string;
        devices: { itemName: string; itemVisibility: boolean }[];
      };
    }>;
  };
  color: string;
  listtitle: string;
  appsettings?: {
    imageBorder?: boolean;
  };
}

// Define types for the FullData array
export interface FullDataType {
  imageUrl: string;
  data: any; // You can further specify the structure of the item data here
  pageType: string;
  itemnavigation: string;
  urlname: string;
  displayname: string;
  introduction: string;
}

const { width } = Dimensions.get('window');

const NewArticleFullImage: React.FC<NewArticleFullImageProps> = (props) => {
  const navigation = useNavigation();
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);
  const colors = useSelector((state: RootState) => state.homeScreen.colors);
  const videoInfo = useSelector((state: RootState) => state.homeScreen.videoCloud);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const [JwData, setJwData] = useState<any[]>([]); // Consider typing this further based on API response
  const [youtubeData, setYoutubeData] = useState<unknown[]>([]);
  const [isYoutubeVideo, setIsYoutubeVideo] = useState(false);
  const FullData: FullDataType[] = [];


  useEffect(() => {
    initVideoInfo();
  }, []);

  const initVideoInfo = async () => {
    try {
      if (!props.datas.playlistId) {
        return;
      }

      if (videoInfo[0].cloudName === 'YouTube') {
        const videoData = await VideoPlayerService.fetchYoutubePlaylist(props.datas.playlistId);
        setYoutubeData(videoData);
        setIsYoutubeVideo(true);
      } else {
        const videoData = await VideoPlayerService.fetchJwplayerPlaylist(props.datas.playlistId);
        setJwData(videoData);
      }
    } catch (error) {
      console.error('Error fetching playlist [NewArticleFullImage]:', error);
    }
  }

  // Helper function to map data into FullData
  function setPlaylistData() {
    if (props.datas != null) {
      props.datas.items.forEach((item) => {
        FullData.push({
          imageUrl: imageUrl + item.itemid.landscapeThumbnail.imagekey,
          data: item,
          pageType: item.itemid.pageType,
          itemnavigation: item.itemid.externalPage,
          urlname: item.itemid.urlName,
          displayname: item.itemid.displayName,
          introduction: item.itemid.introduction,
        });
      });
    }
    if (JwData.playlist != null) {
      JwData.playlist.forEach((item) => {
        FullData.push({
          imageUrl: item.image,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          displayname: item.title,
          introduction: item.description,
        });
      });
    }
    if (youtubeData != null) {
      youtubeData.forEach((item, key) => {
        FullData.push({
          imageUrl: item.thumbnail,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          tag: item.etag,
          mainHeader: item.title,
        });
      });
    }
  }

  // Navigation logic for different page types
  function navigationlist(
    pageType: string,
    itemnavigation: string,
    itemdata: any,
    urlname: string
  ) {
    if (pageType === 'externalPage') {
      return Linking.openURL(itemnavigation);
    } else if (pageType === 'articlePage') {
      return navigation.push('ArticleDetails', {
        item: itemdata,
        color: props.color,
      });
    } else if (pageType === 'seasonPage') {
      return navigation.push('Season', {
        item: itemdata,
        color: props.color,
      });
    } else if (pageType === 'videoPage') {
      if (isYoutubeVideo) {
        return navigation.push('YouTubeDetailsScreen', { item: itemdata, color: props.color, });
      } else {
        return navigation.push('Details', { item: itemdata, color: props.color, });
      }
    } else if (pageType === 'normalPage') {
      return navigation.push('MoreDetail', {
        itemurlname: urlname,
        color: props.color,
      });
    }
  }

  const handleItemPress = (item: any) => {
    navigationlist(item.pageType, item.itemnavigation, item.data, item.urlname);

    /* if (item.data.itemid && item.data.itemid.devices && item.data.itemid.devices.length > 2) {
       if (
         item.data.itemid.devices[2].itemName === 'iOS' ||
         item.data.itemid.devices[2].itemVisibility
       ) {
         navigationlist(item.pageType, item.itemnavigation, item.data, item.urlname);
       } else {
         navigation.push('pagenotfound', { color: props.color });
       }
     } else {
       navigation.push('pagenotfound', { color: props.color });
     }*/
  };

  return (
    <>
      {setPlaylistData()}
      <View style={{ marginBottom: width / 25, marginTop: width / 60 }}>
        <View style={styles(colors, isRTL).inputWrap}>
          <Text style={styles(colors, isRTL).text0}>{props.listtitle}</Text>
        </View>
        <View style={{ marginBottom: width / 100 }}>
          <FlatList
            style={{ marginTop: width / 50 }}
            scrollEnabled={false}
            showsHorizontalScrollIndicator={false}
            data={FullData}
            renderItem={({ item }) => (
              <TouchableOpacity onPress={() => handleItemPress(item)}>
                <View style={styles(colors, isRTL).row}>
                  <View>
                    {item.imageUrl != null ? (
                      <Image
                        PlaceholderContent={<ActivityIndicator />}
                        source={{
                          uri: item.imageUrl != null ? item.imageUrl : '',
                        }}
                        style={{
                          width: width / 1.02,
                          height: width / 1.8,
                          borderWidth: 0,
                          borderColor: '#d35647',
                          borderRadius: props.appsettings?.imageBorder === false ? width / 50 : 0,
                          margin: width / 85,
                        }}
                      />
                    ) : null}
                  </View>
                  <View style={{}}>
                    <Text numberOfLines={3} style={styles(colors, isRTL).textview}>
                      {item.displayname || item.data.title && (
                        <Text style={styles(colors, isRTL).text1}>
                          {item.displayname || item.data.title}
                          {' : '}
                        </Text>
                      )}
                      {item.introduction || item.data.description && (
                        <Text style={styles(colors, isRTL).text2}>{item.data.description}</Text>
                      )}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            )}
          />
        </View>
      </View>
    </>
  );
};

export default NewArticleFullImage;

const styles = (colors, isRTL) => StyleSheet.create({
  row: {
    flex: 1,
    flexDirection: 'column',
  },
  inputWrap: {
    flex: 1,
    borderColor: colors.primaryTextColor,
  },
  text0: {
    color: colors.primaryTextColor,
    fontSize: width / 25,
    fontWeight: 'bold',
    marginHorizontal: width / 40,
    textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',

  },
  text1: {
    color: colors.primaryTextColor,
    fontSize: width / 29,
    fontWeight: 'bold',
    textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',

  },
  text2: {
    color: colors.primaryTextColor,
    fontSize: width / 30,
    textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',

  },
  textview: {
    left: width / 50,
    right: width / 50,
    width: width / 1.02,
    marginBottom: width / 50,
    textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',

  },
});