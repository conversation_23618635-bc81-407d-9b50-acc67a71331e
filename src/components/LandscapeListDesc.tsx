import React, { useEffect, useState } from "react";
import {
  FlatList,
  Text,
  Dimensions,
  View,
  StyleSheet,
  TouchableOpacity,
  Linking,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import { ActivityIndicator } from "react-native";
import { Image } from "react-native-elements";
import { useSelector } from "react-redux";
import { RootState } from "../redux/store";
import { PlaylistItem } from "./FullLandscapeListImageViewDouble";
import { BigPortraitListProps } from "./BigPortraitList";
import { VideoPlayerService } from "../services/VideoPlayerService";

const { width } = Dimensions.get("window");

const LandscapeListDes = (props) => {
  const navigation = useNavigation();
  const color = useSelector((state: RootState) => state.homeScreen.colors);
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);
  const videoInfo = useSelector((state: RootState) => state.homeScreen.videoCloud);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const [jwData, setJwData] = useState<unknown[]>([]);
  const [youtubeData, setYoutubeData] = useState<unknown[]>([]);
  const [isYoutubeVideo, setIsYoutubeVideo] = useState(false);
  const playlistInfo: PlaylistItem[] = [];


  useEffect(() => {
    initVideoInfo();
  }, []);

  const initVideoInfo = async () => {
    try {
      if (!props.datas.playlistId) {
        return;
      }

      if (videoInfo[0].cloudName === 'YouTube') {
        const videoData = await VideoPlayerService.fetchYoutubePlaylist(props.datas.playlistId);
        setYoutubeData(videoData);
        setIsYoutubeVideo(true);
      } else {
        const videoData = await VideoPlayerService.fetchJwplayerPlaylist(props.datas.playlistId);
        setJwData(videoData);
      }
    } catch (error) {
      console.error('Error fetching playlist [LandscapeListDes]:', error);
    }
  };

  function setPlaylistData() {
    {
      if (props.datas != null) {
        props.datas.items.map((item, key) => {
          playlistInfo.push({
            imageUrl: imageUrl + item.itemid.landscapeThumbnail.imagekey,
            data: item,
            pageType: item.itemid.pageType,
            itemnavigation: item.itemid.externalPage,
            urlname: item.itemid.urlName,
            displayname: item.itemid.displayName,
            introduction: item.itemid.introduction,
          });
        });
      }
      if (jwData.playlist != null) {
        jwData.playlist.forEach((item, key) => {
          playlistInfo.push({
            imageUrl: item.image,
            data: item,
            pageType: "videoPage",
            itemnavigation: "",
            urlname: "",
            displayname: item.title,
            introduction: item.description,
          });
        });
      }
    }

    if (youtubeData != null) {
      youtubeData.forEach((item, key) => {
        playlistInfo.push({
          imageUrl: item.thumbnail,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          tag: item.etag,
          mainHeader: item.title,
        });
      });
    }
  }

  function navigationlist(pageType, itemnavigation, itemdata, urlname) {
    if (pageType == "externalPage") {
      return Linking.openURL(itemnavigation);
    } else if (pageType == "articlePage") {
      return navigation.push("ArticleDetails", { item: itemdata, color: props.color });
    } else if (pageType == "seasonPage") {
      return navigation.push("Season", { item: itemdata, color: props.color, });
    } else if (pageType == "videoPage") {
      if (isYoutubeVideo) {
        return navigation.push('YouTubeDetailsScreen', { item: itemdata, color: props.color, });
      } else {
        return navigation.push('Details', { item: itemdata, color: props.color, });
      }
    } else if (pageType == "normalPage") {
      return navigation.push("MoreDetail", { itemurlname: urlname, color: props.color, });
    }
  }

  return (
    setPlaylistData(),
    (
      <View style={{ marginBottom: width / 25, marginTop: width / 60 }}>
        <View style={styles(color, isRTL).row}></View>
        <View style={styles(color, isRTL).inputWrap}>
          <Text style={styles(color, isRTL).text0}>{props.listtitle}</Text>
        </View>
        <View style={{ marginBottom: width / 45 }}>
          <FlatList
            style={{ marginTop: width / 50 }}
            // columnWrapperStyle={styles(color, isRTL).row}
            scrollEnabled={false}
            showsHorizontalScrollIndicator={false}
            data={playlistInfo}
            renderItem={({ item }) => (
              <TouchableOpacity
                onPress={() => {
                  (item.data.itemid !== undefined
                    ? item.data.itemid.devices[2].itemName == "iOS"
                    : null && item.data.itemid !== undefined
                      ? item.data.itemid.devices[2].itemVisibility == true
                      : null) || item.data.itemid == undefined
                    ? navigationlist(
                      item.pageType,
                      item.itemnavigation,
                      item.data,
                      item.urlname
                    )
                    : navigation.push("pagenotfound", {
                      color: props.color,
                    });
                }}
              >
                <View style={styles(color, isRTL).row}>
                  <View>
                    {item.imageUrl != null ? (
                      <Image
                        PlaceholderContent={<ActivityIndicator />}
                        source={{
                          uri: item.imageUrl != null ? item.imageUrl : null,
                        }}
                        style={{
                          width: width / 2.5,
                          height: width / 4,
                          borderWidth: 0,
                          borderColor: "#d35647",
                          borderRadius:
                            props.appsettings !== undefined
                              ? props.appsettings.imageBorder === false
                                ? width / 50
                                : 0
                              : null,
                          // resizeMode:'contain',
                          margin: width / 85,
                          // marginTop:5
                        }}
                      />
                    ) : null}
                  </View>
                  <View>
                    <Text numberOfLines={6}>
                      <Text style={styles(color, isRTL).text1}>{item.displayname} : </Text>
                      <Text style={styles(color, isRTL).text2}>{item.introduction}</Text>
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            )}
          />
        </View>
      </View>
    )
  );
};

export default LandscapeListDes;



const styles = (color, isRTL) => StyleSheet.create({
  row: {
    flex: 1,
    flexDirection: "row",
    width: width / 1.9,
    marginBottom: width / 50,
  },
  inputWrap: {
    flex: 1,
    borderColor: color.primaryTextColor,
  },
  text0: {
    color: color.primaryTextColor,
    fontSize: width / 25,
    fontWeight: "bold",
    marginHorizontal: width / 40,
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
  },
  text1: {
    color: color.primaryTextColor,
    fontSize: width / 30,
    fontWeight: "bold",
    marginHorizontal: width / 50,
    marginBottom: width / 100,
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
  },
  text2: {
    color: color.primaryTextColor,
    fontSize: width / 34,
    marginHorizontal: width / 50,
    marginBottom: width / 100,
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
  },
});