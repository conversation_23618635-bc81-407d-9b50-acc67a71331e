import React, { useEffect, useState } from 'react';
import {
  FlatList,
  Text,
  Dimensions,
  View,
  StyleSheet,
  TouchableOpacity,
  Linking,
  ViewStyle,
  ImageStyle,
  TextStyle,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ActivityIndicator } from 'react-native';
import { Image } from 'react-native-elements';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { VideoPlayerService } from '../services/VideoPlayerService';
import { BigPortraitListProps } from './BigPortraitList';

const { width } = Dimensions.get('window');
export interface PlaylistItem {
  imageUrl?: string;
  data: any; // Replace 'any' with the actual type of 'item'
  pageType: string;
  itemnavigation: string;
  urlname: string;
  tag: string | number; // Adjust based on the actual type of 'tag'
  mainHeader: string;
}

const FullLandscapeListImageViewDouble = (props) => {
  const color = useSelector((state: RootState) => state.homeScreen.colors);
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);
  const videoInfo = useSelector((state: RootState) => state.homeScreen.videoCloud);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const navigation = useNavigation();
  const [jwData, setJwData] = useState<unknown[]>([]);
  const [youtubeData, setYoutubeData] = useState<unknown[]>([]);
  const [isYoutubeVideo, setIsYoutubeVideo] = useState(false);
  const playlistInfo: PlaylistItem[] = [];

  useEffect(() => {
    initVideoInfo();
  }, []);

  const initVideoInfo = async () => {
    try {
      if (!props.datas.playlistId) {
        return;
      }

      if (videoInfo[0].cloudName === 'YouTube') {
        const videoData = await VideoPlayerService.fetchYoutubePlaylist(props.datas.playlistId);
        setYoutubeData(videoData);
        setIsYoutubeVideo(true);
      } else {
        const videoData = await VideoPlayerService.fetchJwplayerPlaylist(props.datas.playlistId);
        setJwData(videoData);
      }
    } catch (error) {
      console.error('Error fetching playlist [FullLandscapeListImageViewDouble]:', error);
    }

    // try {
    //   const response = await axios.get(
    //     `https://www.googleapis.com/youtube/v3/playlistItems`,
    //     {
    //       params: {
    //         part: 'snippet',
    //         playlistId: props.datas.playlistId,
    //         maxResults: 50,
    //         key: videoInfo[0].cloudKey,
    //       },
    //     }
    //   );

    //   const videoData = response.data.items.map((item) => {
    //     console.log("youtube item=====>", item);

    //     return ({
    //       title: item.snippet.title,
    //       videoId: item.snippet.resourceId.videoId,
    //       thumbnail: item.snippet.thumbnails.medium.url,
    //     });
    //   });


    //   setYoutubeData(videoData);
    // } catch (error) {
    //   console.error('Error fetching playlist:', error);
    // }
  };

  function setPlaylistData() {
    if (props.datas != null) {
      props.datas.items.forEach((item, key) => {
        playlistInfo.push({
          imageUrl: imageUrl + (item.itemid.landscapeThumbnail != null ? item.itemid.landscapeThumbnail.imagekey : ""),
          data: item,
          pageType: item.itemid.pageType,
          itemnavigation: item.itemid.externalPage,
          urlname: item.itemid.urlName,
          tag: item.itemid.tag,
          mainHeader: item.itemid.mainHeader,
        });
      });
    }

    if (jwData.playlist != null) {
      jwData.playlist.forEach((item, key) => {
        playlistInfo.push({
          imageUrl: isYoutubeVideo ? item.thumbnail : item.image,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          tag: item.MediaType,
          mainHeader: item.title,
        });
      });
    }

    if (youtubeData != null) {
      youtubeData.forEach((item, key) => {
        playlistInfo.push({
          imageUrl: item.thumbnail,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          tag: item.etag,
          mainHeader: item.title,
        });
      });
    }
  }

  function setNavigationInfo(index: number) {
    const itemData = isYoutubeVideo ? playlistInfo[index]?.data : playlistInfo[index]?.data?.itemid;
    if (itemData) {
      handleNavigationTo(
        playlistInfo[index].pageType,
        playlistInfo[index].itemnavigation,
        playlistInfo[index].data,
        playlistInfo[index].urlname
      );
    }
    // if (itemData || (itemData.devices?.[2]?.itemName === 'iOS' && itemData.devices?.[2]?.itemVisibility === true)) {
    // } else {
    //   navigation.push('pagenotfound', { color: props.color });
    // }
  }

  const handleNavigationTo = (
    pageType: string,
    itemnavigation: string,
    itemdata: any,
    urlname: string
  ) => {
    if (pageType == 'externalPage') {
      return Linking.openURL(itemnavigation);
    } else if (pageType === 'articlePage') {
      return navigation.push('ArticleDetails', { item: itemdata, color: props.color, });
    } else if (pageType === 'seasonPage') {
      return navigation.push('Season', { item: itemdata, color: props.color, });
    } else if (pageType === 'videoPage') {
      if (isYoutubeVideo) {
        return navigation.push('YouTubeDetailsScreen', { item: itemdata, color: props.color, });
      } else {
        return navigation.push('Details', { item: itemdata, color: props.color, });
      }
    } else if (pageType === 'normalPage') {
      return navigation.push('MoreDetail', { itemurlname: urlname, color: props.color, });
    }
  }

  const ImageComp = ({ index, imageUrl, imgStyle }: { index: number, imageUrl: string | null, imgStyle: ImageStyle | ViewStyle | TextStyle }) => {
    return (
      (imageUrl !== null && <TouchableOpacity
        onPress={() => {
          setNavigationInfo(index);
        }}
      >
        <Image
          PlaceholderContent={<ActivityIndicator />}
          source={{ uri: imageUrl }}
          style={imgStyle}
        />
      </TouchableOpacity>
      )
    )
  }

  return (
    setPlaylistData(),
    (
      <View style={{ marginBottom: width / 25, marginTop: width / 25 }}>
        <View style={styles(color, props, isRTL).row}></View>
        <View style={styles(color, props, isRTL).inputWrap}>
          <Text style={styles(color, props, isRTL).text1}>{props.listtitle}</Text>
        </View>
        <View style={{ marginBottom: width / 45, marginTop: width / 50, }}>
          <FlatList
            numColumns={2} // set number of columns
            // columnWrapperStyle={styles(color, props).row}
            scrollEnabled={false}
            showsHorizontalScrollIndicator={false}
            data={playlistInfo}
            renderItem={({ item, index }) => (
              <ImageComp index={index} imageUrl={item.imageUrl != null ? item.imageUrl : null} imgStyle={styles(color, props).img2} />
            )}
          />
        </View>
      </View >
    )
  );
};

export default FullLandscapeListImageViewDouble;


const styles = (color, props, isRTL) => StyleSheet.create({
  row: {
    flex: 1,
    justifyContent: 'space-around',
    direction: isRTL ? 'rtl' : 'ltr',
  },
  inputWrap: {
    flex: 1,
    borderColor: color.primaryTextColor,
    alignSelf: isRTL ? 'flex-end' : 'flex-start',
    direction: isRTL ? 'rtl' : 'ltr',
  },
  text1: {
    color: color.primaryTextColor,
    fontSize: width / 25,
    fontWeight: 'bold',
    // left: width / 40,
    marginHorizontal: width / 40,
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
  },
  text2: {
    color: color.primaryTextColor,
    fontSize: width / 30,
    fontWeight: 'bold',
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
    marginHorizontal: width / 40,
  },
  img1: {
    width: width / 1.025,
    height: width / 1.8,
    borderWidth: 0,
    borderColor: '#d35647',
    borderRadius:
      props.appsettings !== undefined
        ? props.appsettings.imageBorder === false
          ? width / 50
          : 0
        : 0,
    // resizeMode:'contain',
    margin: width / 85,
    // marginTop:5
  },
  img2: {
    width: width / 2.1,
    height: width / 3.6,
    borderWidth: 0,
    borderColor: '#d35647',
    borderRadius:
      props.appsettings !== undefined
        ? props.appsettings.imageBorder === false
          ? width / 50
          : 0
        : 0,
    // resizeMode:'contain',
    margin: width / 85,
    // marginTop:5
  },
});