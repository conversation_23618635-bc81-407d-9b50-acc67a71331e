import React, { useEffect, useState } from 'react';
import { FlatList, Text, Dimensions, View, TouchableOpacity, Linking, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { VideoPlayerService } from '../services/VideoPlayerService';

const { width } = Dimensions.get('window');

interface Item {
  itemid?: {
    landscapeThumbnail?: { imagekey?: string };
    pageType?: string;
    externalPage?: string;
    urlName?: string;
    tag?: string;
    mainHeader?: string;
    devices?: { itemName?: string; itemVisibility?: boolean }[];
  };
}

interface JWItem {
  image: string;
  MediaType?: string;
  title?: string;
}

interface Props {
  datas: {
    playlistId?: string;
    items?: Item[];
  };
  listtitle: string;
  color: string;
  appsettings?: {
    imageBorder?: boolean;
  };
}

const FullLandscapeListImageView: React.FC<Props> = ({ datas, listtitle, color, appsettings }) => {
  const navigation = useNavigation();
  const colors = useSelector((state: RootState) => state.homeScreen.colors);
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);
  const videoInfo = useSelector((state: RootState) => state.homeScreen.videoCloud);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const [JwData, setJwData] = useState<{ playlist?: JWItem[] }>({});
  const [FullData, setFullData] = useState<
    {
      imageUrl: string;
      data: unknown;
      pageType: string;
      itemnavigation: string;
      urlname: string;
      tag?: string;
      mainHeader?: string;
    }[]
  >([]);
  const [youtubeData, setYoutubeData] = useState<unknown[]>([]);
  const [isYoutubeVideo, setIsYoutubeVideo] = useState(false);

  useEffect(() => {
    initVideoInfo();
  }, [datas.playlistId]);

  const initVideoInfo = async () => {
    try {
      if (!datas.playlistId) {
        return;
      }
      if (videoInfo[0].cloudName === 'YouTube') {
        const videoData = await VideoPlayerService.fetchYoutubePlaylist(datas.playlistId);
        setYoutubeData(videoData);
        setIsYoutubeVideo(true);
      } else {
        const videoData = await VideoPlayerService.fetchJwplayerPlaylist(datas.playlistId) as JWItem[];
        setJwData(videoData);
      }
    } catch (error) {
      console.error('Error fetching playlist [FullLandscapeListImageView]:', error);
    }
  }

  useEffect(() => {
    const updatedData: typeof FullData = [];

    datas.items?.forEach((item) => {
      updatedData.push({
        imageUrl: imageUrl + (item.itemid?.landscapeThumbnail?.imagekey ?? ''),
        data: item,
        pageType: item.itemid?.pageType ?? '',
        itemnavigation: item.itemid?.externalPage ?? '',
        urlname: item.itemid?.urlName ?? '',
        tag: item.itemid?.tag,
        mainHeader: item.itemid?.mainHeader,
      });
    });

    JwData.playlist?.forEach((item) => {
      updatedData.push({
        imageUrl: item.image,
        data: item,
        pageType: 'videoPage',
        itemnavigation: '',
        urlname: '',
        tag: item.MediaType,
        mainHeader: item.title,
      });
    });
    if (youtubeData != null) {
      youtubeData.forEach((item, key) => {
        updatedData.push({
          imageUrl: item.thumbnail,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          tag: item.etag,
          mainHeader: item.title,
        });
      });
    }

    setFullData(updatedData);
  }, [datas.items, JwData]);

  const handleNavigationTo = (
    pageType: string,
    itemnavigation: string,
    itemdata: unknown,
    urlname: string
  ) => {

    switch (pageType) {
      case 'externalPage':
        return Linking.openURL(itemnavigation);
      case 'articlePage':
        return navigation.navigate('ArticleDetails', { item: itemdata, color });
      case 'seasonPage':
        return navigation.navigate('Season', { item: itemdata, color });
      case 'videoPage':
        if (isYoutubeVideo) {
          return navigation.navigate('YouTubeDetailsScreen', { item: itemdata, color: props.color, });
        } else {
          return navigation.navigate('Details', { item: itemdata, color: props.color, });
        }
      case 'normalPage':
        return navigation.navigate('MoreDetail', { itemurlname: urlname, color });
      default:
        return;
    }
  };

  return (
    <View style={{ marginBottom: width / 25, marginTop: width / 25 }}>
      <View style={{
        flex: 1, justifyContent: 'space-around',
        direction: isRTL ? 'rtl' : 'ltr',
      }} />
      <View style={{
        flex: 1, borderColor: colors.primaryTextColor, alignItems: isRTL ? 'flex-end' : 'flex-start', alignSelf: isRTL ? 'flex-end' : 'flex-start',
        direction: isRTL ? 'rtl' : 'ltr',
      }}>
        <Text
          style={{
            color: colors.primaryTextColor,
            fontSize: width / 25,
            fontWeight: 'bold',
            marginHorizontal: width / 40,
            textAlign: isRTL ? 'right' : 'left',
            writingDirection: isRTL ? 'rtl' : 'ltr',
          }}>
          {listtitle}
        </Text>
      </View>
      <View style={{ marginBottom: width / 45, marginTop: width / 50, alignItems: isRTL ? 'flex-end' : 'flex-start' }}>
        {FullData.length > 0 && (
          <TouchableOpacity
            onPress={() =>
              handleNavigationTo(
                FullData[0].pageType,
                FullData[0].itemnavigation,
                FullData[0].data,
                FullData[0].urlname
              )
            }>
            <Image
              source={{ uri: FullData[0].imageUrl || undefined }}
              style={{
                width: width / 1.025,
                height: width / 1.8,
                borderRadius: appsettings?.imageBorder === false ? width / 50 : 0,
                margin: width / 85,
              }}
            />
          </TouchableOpacity>
        )}
        <FlatList
          numColumns={2}
          scrollEnabled={false}
          showsHorizontalScrollIndicator={false}
          data={FullData.slice(1)}
          renderItem={({ item }) => (
            <TouchableOpacity
              onPress={() =>
                handleNavigationTo(item.pageType, item.itemnavigation, item.data, item.urlname)
              }>
              <Image
                source={{ uri: item.imageUrl || undefined }}
                style={{
                  width: width / 2.1,
                  height: width / 3.6,
                  borderRadius: appsettings?.imageBorder === false ? width / 50 : 0,
                  margin: width / 85,
                }}
              />
            </TouchableOpacity>
          )}
        />
      </View>
    </View>
  );
};

export default FullLandscapeListImageView;
