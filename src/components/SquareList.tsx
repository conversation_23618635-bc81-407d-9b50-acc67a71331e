import React from 'react';
import { FlatList, Text, Dimensions, View, StyleSheet, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ActivityIndicator } from 'react-native';
import { Image } from 'react-native-elements';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';


const { width } = Dimensions.get('window');

const SquareList = (props) => {
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const navigation = useNavigation();
  const color = useSelector((state: RootState) => state.homeScreen.colors);
  const styles = StyleSheet.create({
    row: {
      flex: 1,
      flexDirection: 'row',
    },
    inputWrap: {
      flex: 1,
      borderColor: color.primaryTextColor,
    },
    text1: {
      color: color.primaryTextColor,
      fontSize: width / 30,
      fontWeight: 'bold',
      marginHorizontal: width / 40,
      textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',

    },
    text2: {
      color: color.primaryTextColor,
      fontSize: width / 30,
      fontWeight: 'bold',
      textAlign: 'right',
      marginHorizontal: width / 40,
    },
  });
  return (
    <View style={{ marginBottom: width / 25, marginTop: width / 60, direction: isRTL ? 'rtl' : 'ltr' }}>
      <View style={styles.row}>
        <View style={styles.inputWrap}>
          <Text style={styles.text1}>{props.listtitle}</Text>
        </View>
        <View style={styles.inputWrap}>
          {props.seemoretextvis == true ? (
            <TouchableOpacity
              style={styles.text2}
              onPress={() => {
                navigation.navigate('SeeMore', {
                  itemId: props.listtitle,
                  listdatas: props.datas,
                  comp: props.seemorecomp,
                  color: props.color,
                });
              }}>
              <Text style={styles.text2}>{props.seemoretext}</Text>
            </TouchableOpacity>
          ) : null}
        </View>
      </View>

      <FlatList
        style={{ marginTop: width / 50 }}
        horizontal
        showsHorizontalScrollIndicator={false}
        data={props.datas}
        renderItem={({ item }) => (
          <TouchableOpacity
            onPress={() => {
              navigation.navigate('Details', {
                itemId: item.title,
                color: color,
              });
            }}>
            <Image
              PlaceholderContent={<ActivityIndicator />}
              source={{ uri: item.imageUrl }}
              style={{
                width: width / 4,
                height: width / 4,
                borderWidth: 0,
                borderColor: '#d35647',
                borderRadius:
                  props.appsettings !== undefined
                    ? props.appsettings.imageBorder === false
                      ? width / 50
                      : 0
                    : null,
                //   resizeMode:'fill',
                margin: width / 150,
                marginBottom: width / 50,
              }}
            />
          </TouchableOpacity>
        )}
      />
    </View>
  );
};

export default SquareList;
