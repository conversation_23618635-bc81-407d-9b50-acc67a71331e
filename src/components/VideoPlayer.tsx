import React, { useRef, useState, useEffect, useCallback } from "react";
import { View, Text, Dimensions, ActivityIndicator } from "react-native";
import Video, { VideoRef } from "react-native-video";
import { AppService } from "../services/AppService";
import { useSelector } from "react-redux";
import { RootState } from "../redux/store";
import { useFocusEffect } from "@react-navigation/native";

const VideoPlayer = () => {
  // Constant values for title and description

  const videoTitle =
    AppService.appId === "6791c1e0ef1fe5183be52ce8"
      ? "Qatar Equestrian Federation (QEF)"
      : null;
  const videoDescription =
    AppService.appId === "6791c1e0ef1fe5183be52ce8"
      ? "Watch live races and exclusive equestrian content from the Qatar Equestrian Federation."
      : null;
  const videoSource =
    AppService.appId === "6791c1e0ef1fe5183be52ce8"
      ? "https://www.golfsaudi.com/storage/2024-04-15/gARtzSL8Hb7La9LWmKxI6HBDxxgl5r6SW15Xu2dJ.mp4"
      : AppService.appId === "67b6ac45984a830020a10f2f"
        ? "https://devlqc.citrusdev.com/files/QEF-Facilities.mp4"
        : AppService.appId === "67b80532984a830020a11eae"
          ? "https://devlqc.citrusdev.com/files/al-araby-network.mp4"
          : AppService.appId === "67bedfa6984a830020a13304"
            ? "https://devlqc.citrusdev.com/files/bahrin-news.mp4"
            : "";
  const videoRef = useRef<VideoRef>(null);
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  const background = { uri: videoSource };
  const { width, height } = Dimensions.get('window');
  const colors = useSelector((state: RootState) => state.homeScreen.colors);
  const [isLoaded, setIsLoaded] = useState<boolean>(false);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);


  useFocusEffect(
    useCallback(() => {
      return () => {
        console.log("Cleanup function running...");
        if (videoRef.current) {
          console.log("Pausing video...");
          videoRef.current.pause();
        }
      };
    }, [])
  );

  return (
    <View style={{ left: 0, right: 0, width, height: videoSource ? width * 0.5625 : 0 }}>
      {/* Show Loader until the video starts */}

      {videoSource && (
        <>
          {!isLoaded && (
            <ActivityIndicator size="large" style={{ zIndex: 1, position: 'absolute', top: '50%', left: '50%', transform: [{ translateX: -25 }, { translateY: -25 }] }} />
          )}
          <Video
            source={background}
            // Store reference  
            ref={videoRef}
            style={{
              height: "100%",
            }}
            renderLoader={!isLoaded}
            onLoad={() => {
              console.log('Video loaded');
              setIsLoaded(true);
            }}
            onBuffer={({ isBuffering }) => {
              if (isBuffering) {
                setIsLoaded(false);
              }
            }}
          />
        </>
      )}
      {videoTitle && videoDescription &&
        (<View
          style={{

            bottom: 50, // Aligns content near the bottom
            // left: 20, // Aligns content to the left
            alignContent: isRTL ? 'flex-end' : 'flex-start',
            marginHorizontal: 8,
            maxWidth: "100%"
          }}
        >
          <Text
            style={{
              fontSize: 14,
              fontWeight: "bold",
              color: colors.primaryTextColor,
              overflow: 'scroll',
              textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
            }}
            numberOfLines={1}
          >
            {videoTitle}
          </Text>
          <Text

            style={{
              fontSize: 12,
              color: colors.secondaryTextColor,
              overflow: 'scroll',
              textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
            }}
            numberOfLines={2}
          >
            {videoDescription}
          </Text>
        </View>
        )
      }
    </View >
  );
};

export default VideoPlayer;