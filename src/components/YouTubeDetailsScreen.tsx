import * as React from 'react';
import {
    Text,
    Dimensions,
    View,
    StyleSheet,
    SafeAreaView,
    ScrollView,
    Share,
    TouchableOpacity,
} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/Ionicons';
import { useState } from 'react';
import axios from 'axios';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Spinner from 'react-native-loading-spinner-overlay/lib';
import PortraitList from './PortraitList';
import RoundList from './RoundList';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppService } from '../services/AppService';
import { WebView } from 'react-native-webview';
import { VideoPlayerService } from '../services/VideoPlayerService';
import { YoutubeData } from '../config/interfaces/YoutubeDataInterface';
import InPlayer from '@inplayer-org/inplayer.js';

const { width } = Dimensions.get('window');

const YouTubeDetailsScreen = ({ route }) => {
    const navigation = useNavigation();
    const { item, color } = route.params;
    const colors = useSelector((state: RootState) => state.homeScreen.colors);
    // const [youTubeData, setYouTubeData] = useState(YoutubeData);
    const [youTubeDataDetails, setYouTubeDataDetails] = useState<YoutubeData>();
    const [videoId, setVideoId] = useState('');
    const [selectedMenu, setSelectedMenu] = useState(0);
    const [spinner, setSpinner] = useState(true);
    const [loading, setLoading] = useState(false);
    const { isRTL } = useSelector((state: RootState) => state.homeScreen);


    const styles = StyleSheet.create({
        container: {
            flex: 1,
            backgroundColor: colors.primaryBackgroundColor,
        },
        videoContainer: {
            width: '100%',
            height: width * 0.5625, // Standard 16:9 aspect ratio
            backgroundColor: '#000',
        },
        webView: {
            flex: 1,
            backgroundColor: '#000',
        },
        watchNowButtonContainer: {
            flexDirection: 'row',
            backgroundColor: colors.primaryHighlightColor,
            alignItems: 'center',
            justifyContent: 'center',
            marginLeft: width / 22,
            marginRight: width / 22,
            marginTop: 0,
            marginBottom: width / 40,
            width: width / 1.1,
            height: width / 12,
            borderRadius: 5,
        },
        subButton: {
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            marginLeft: width / 22,
            marginRight: width / -44,
            marginTop: 0,
            marginBottom: width / 40,
            width: width / 2.25,
            height: width / 12,
            borderRadius: 5,
            borderWidth: 1,
            borderColor: colors.primaryTextColor,
        },
        relatedButton: {
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            marginLeft: width / 40,
            marginRight: width / -44,
            marginTop: 0,
            marginBottom: width / 40,
            width: width / 2.2,
            height: width / 12,
            borderWidth: selectedMenu === 2 ? 3 : 0,
            borderColor: 'transparent',
            borderBottomColor: colors.primaryHighlightColor,
            borderRadius: 0,
        },
        detailsButton: {
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            marginLeft: width / 22,
            marginRight: width / -44,
            marginTop: 0,
            marginBottom: width / 40,
            width: width / 2.2,
            height: width / 12,
            borderWidth: selectedMenu === 1 ? 3 : 0,
            borderColor: 'transparent',
            borderBottomColor: colors.primaryHighlightColor,
            borderRadius: 0,
        },
    });

    useFocusEffect(
        React.useCallback(() => {
            if (!youTubeDataDetails) {
                getYouTubeVideoInfo();
            }

            // getItemValue();
        }, [youTubeDataDetails])
    );


    const getYouTubeVideoInfo = async () => {
        const videoID = item.videoId;
        if (!videoID) {
            console.log("Invalid videoID:", videoID);
            return;
        }

        try {
            const { data } = await axios.get(`https://www.googleapis.com/youtube/v3/videos`, {
                params: {
                    part: "snippet,contentDetails",
                    id: videoID,
                    key: VideoPlayerService.videoInfo[0].cloudKey,
                },
            });

            if (!data.items || data.items.length === 0) {
                console.log("Invalid YouTube video data");
                return;
            }

            console.log("Video Title:", data.items[0].snippet.title);
            setVideoId(data.items[0].id);
            // setYouTubeData(data);
            setYouTubeDataDetails(data);
            setSelectedMenu(data.items[0].snippet.categoryId === "1" ? 1 : 0); // Example: category ID check
        } catch (e) {
            console.error("Error fetching YouTube data:", e);
        } finally {
            setSpinner(false);
        }
    };

    const title = youTubeDataDetails ? youTubeDataDetails?.items[0].snippet.title : '';
    const rating = youTubeDataDetails ? youTubeDataDetails?.items[0].contentDetails.contentRating.rating : '';


    const onShare = async () => {
        try {
            await Share.share({
                message: `https://www.youtube.com/watch?v=${videoId}`,
            });
        } catch (error) {
            alert(error.message);
        }
    };

    async function updateprofile() {
        const setting = {};
        const watchHistory = [];
        const profile = {};
        console.log('sgdffuy', InPlayer.Account.getToken());
        const favourites = [
            {
                videoId: videoId,
                title: title,
                type: 'page',
                pageType: 'seasonPage',
                imageUrl: youTubeDataDetails ?
                    youTubeDataDetails?.items[0].snippet.thumbnails.default.url : '',
                urlName: title,
                date: youTubeDataDetails ?
                    youTubeDataDetails?.items[0].snippet.publishedAt : '',
            },
        ];

        InPlayer.Account.updateAccount(
            {
                metadata: { favourites: JSON.stringify(favourites) },
            },
            InPlayer.Account.token
        )
            .then((data) => console.log(data))
            .catch((error) => error.response.json().then((data) => console.log('Error', data)));
    }


    const VideoPlayer = () => (
        <View style={styles.videoContainer}>
            <WebView
                allowsFullscreenVideo
                allowsInlineMediaPlayback
                mediaPlaybackRequiresUserAction
                javaScriptEnabled
                source={{
                    uri: `https://www.youtube.com/embed/${videoId}?rel=0&enablejsapi=1&playsinline=1&showInfo=0&controls=1&fullscreen=1`,
                }}
                style={styles.webView}
            />
        </View>
    );


    return (
        <SafeAreaView style={styles.container}>
            <Spinner
                visible={spinner}
                textContent={'Loading...'}
                textStyle={{ color: colors.primaryHighlightColor }}
                overlayColor={colors.primaryBackgroundColor}
                color={colors.primaryHighlightColor}
            />

            <ScrollView showsVerticalScrollIndicator={false} nestedScrollEnabled={true}>
                <View>
                    <VideoPlayer />
                </View>


                <Text
                    style={{
                        color: colors.primaryTextColor,
                        margin: width / 22,
                        fontWeight: 'bold',
                        fontSize: width / 20,
                        textAlign: isRTL ? 'right' : 'left',
                        writingDirection: isRTL ? 'rtl' : 'ltr',
                        marginTop: width / 25,
                        marginBottom: width / 40,
                    }}>
                    {title}
                </Text>
                <View
                    style={{
                        marginLeft: width / 22,
                        marginRight: width / 22,
                        marginTop: 0,
                        width: width / 1.1,
                        marginBottom: 0,
                    }}>
                    <View style={{ flexDirection: 'row' }}>
                        {rating != null && rating != '' ? (
                            <Text
                                style={{
                                    marginLeft: 0,
                                    fontSize: width / 38,
                                    textAlign: isRTL ? 'right' : 'left',
                                    writingDirection: isRTL ? 'rtl' : 'ltr',
                                    marginTop: 1,
                                    marginBottom: width / 25,
                                    color: '#808080',
                                }}>
                                Rating : {rating}
                            </Text>
                        ) : null}
                        {/* {JwDataDetails.VIdeoDuration != '' && JwDataDetails.VIdeoDuration != null ? (
                            <Text
                                style={{
                                    marginLeft: width / 38,
                                    fontSize: width / 38,
                                    textAlign: isRTL ? 'right' : 'left',
            writingDirection: isRTL ? 'rtl' : 'ltr',
                                    marginTop: 1,
                                    marginBottom: width / 25,
                                    color: '#808080',
                                }}>
                                {secondsToHms(JwDataDetails.duration)}
                            </Text>
                        ) : null} */}
                        {/* {JwDataDetails.ReleaseOn != '' && JwDataDetails.ReleaseOn != null ? (
                            <Text
                                style={{
                                    marginLeft: width / 38,
                                    fontSize: width / 38,
                                    textAlign: isRTL ? 'right' : 'left',
            writingDirection: isRTL ? 'rtl' : 'ltr',
                                    marginTop: 1,
                                    marginBottom: width / 25,
                                    color: '#808080',
                                }}>
                                {JwDataDetails.ReleaseOn}
                            </Text>
                        ) : null} */}
                        {/* {JwDataDetails.MPAARating != '' && JwDataDetails.MPAARating != null ? (
                            <Text
                                style={{
                                    marginLeft: width / 38,
                                    fontSize: width / 38,
                                    textAlign: isRTL ? 'right' : 'left',
            writingDirection: isRTL ? 'rtl' : 'ltr',
                                    marginTop: 0,
                                    marginBottom: width / 25,
                                    color: '#808080',
                                    borderWidth: 1,
                                    borderColor: '#808080',
                                    borderRadius: 5,
                                }}>
                                {JwDataDetails.MPAARating}
                            </Text>
                        ) : null} */}
                    </View>
                    {/* {JwDataDetails.Genre != '' && JwDataDetails.Genre != null ? (
                        <Text
                            style={{
                                marginLeft: 0,
                                fontSize: width / 38,
                                textAlign: isRTL ? 'right' : 'left',
            writingDirection: isRTL ? 'rtl' : 'ltr',
                                marginTop: width / -30,
                                marginBottom: width / 25,
                                color: '#808080',
                            }}>
                            {JwDataDetails.Genre}
                        </Text>
                    ) : null} */}
                    {/* {JwDataDetails.AudioTracks != '' && JwDataDetails.AudioTracks != null ? (
                        <Text
                            style={{
                                marginLeft: 0,
                                fontSize: width / 38,
                                textAlign: isRTL ? 'right' : 'left',
            writingDirection: isRTL ? 'rtl' : 'ltr',
                                marginTop: width / -30,
                                marginBottom: width / 25,
                                color: '#808080',
                            }}>
                            Audio : {JwDataDetails.AudioTracks}
                        </Text>
                    ) : null} */}
                </View>
                {/* <View style={{ flexDirection: 'row' }}>
                    {JwDataDetails.MediaType == 'Movie' ? (
                        <TouchableOpacity
                            onPress={() => {
                                navigation.push('FullScreen', {
                                    itemurl: JwDataDetails.MovieId,
                                    image: JwDataDetails.image,
                                    color: color,
                                });
                            }}
                            style={styles.watchnowbuttoncontainer}>
                            <View style={{ flexDirection: 'row' }}>
                                <MaterialCommunityIcons
                                    name="play"
                                    color={colors.elementForegroundColor}
                                    size={width / 26}
                                />
                                <Text
                                    style={{
                                        fontSize: width / 30,
                                        color: colors.elementForegroundColor,
                                        marginLeft: 5,
                                    }}>
                                    Watch Now
                                </Text>
                            </View>
                        </TouchableOpacity>
                    ) : null}
                </View> */}
                <View style={{ flexDirection: 'row' }}>
                    <TouchableOpacity onPress={updateprofile} style={styles.subButton}>
                        <View style={{ flexDirection: 'row' }}>
                            <MaterialCommunityIcons
                                name="star"
                                color={colors.primaryHighlightColor}
                                size={width / 26}
                            />
                            <Text
                                style={{
                                    fontSize: width / 30,
                                    color: colors.primaryHighlightColor,
                                    marginLeft: 5,
                                }}>
                                {isRTL ? 'المفضلة' : 'Favourite'}
                            </Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={onShare} style={styles.subButton}>
                        <View style={{ flexDirection: 'row' }}>
                            <MaterialCommunityIcons
                                name="share"
                                color={colors.primaryTextColor}
                                size={width / 25}
                            />
                            <Text
                                style={{
                                    fontSize: width / 30,
                                    color: colors.primaryTextColor,
                                    marginLeft: 5,
                                }}>
                                {isRTL ? 'مشاركة' : 'Share'}
                            </Text>
                        </View>
                    </TouchableOpacity>
                </View>
                {selectedMenu != 0 ? (
                    <View style={{ flexDirection: 'row', marginTop: width / 30 }}>
                        <TouchableOpacity
                            onPress={() => {
                                setSelectedMenu(1);
                            }}
                            style={styles.detailsButton}>
                            <View style={{ flexDirection: 'row' }}>
                                <Text
                                    style={{
                                        fontSize: width / 30,
                                        color: colors.primaryTextColor,
                                        marginLeft: 5,
                                    }}>
                                    {isRTL ? 'تفاصيل' : 'Details'}
                                </Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => {
                                setSelectedMenu(2);
                            }}
                            style={styles.relatedButton}>
                            <View style={{ flexDirection: 'row' }}>
                                <Text
                                    style={{
                                        fontSize: width / 30,
                                        color: colors.primaryTextColor,
                                        marginLeft: 5,
                                    }}>
                                    {isRTL ? 'ذو صلة' : 'Related'}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                ) : null}
                <View style={{ flexDirection: 'row' }}></View>
                {selectedMenu == 1 || selectedMenu == 0 ? (
                    <View>
                        {/* {youTubeDataDetails.MediaType == 'Movie' ? (
                            <Text
                                style={{
                                    color: colors.primaryTextColor,
                                    fontSize: width / 30,
                                    fontWeight: 'bold',
                                    marginHorizontal: width / 40,
                                }}>
                                Synopsis
                            </Text>
                        ) : null} */}
                        <Text
                            style={{
                                margin: width / 22,
                                fontSize: width / 32,
                                textAlign: isRTL ? 'right' : 'left',
                                writingDirection: isRTL ? 'rtl' : 'ltr',
                                marginTop: width / 30,
                                marginBottom: width / 25,
                                color: colors.primaryTextColor,
                            }}>
                            {youTubeDataDetails?.items[0].snippet.description}
                        </Text>
                        {/* {JwDataDetails.MediaType == 'Movie' ? (
                            <RoundList
                                color={color}
                                seemorecomp={''}
                                listtitle={'CAST & CREW'}
                                seemoretext={''}
                                seemoretextvis={false}
                                url={''}
                                urltype={''}
                                datas={JwDataDetails}
                            />
                        ) : null} */}
                    </View>
                ) : null}
                {/* {selectedMenu == 2 || selectedMenu == 0 ? (
                    JwDataDetails.Recommendation != '' ? (
                        <PortraitList
                            color={colors}
                            seemorecomp={''}
                            listtitle={''}
                            seemoretext={''}
                            seemoretextvis={false}
                            url={''}
                            urltype={''}
                            playlistId={JwDataDetails.Recommendation}
                            datas={null}
                        />
                    ) : null
                ) : null} */}
            </ScrollView>
        </SafeAreaView >
    );
};

export default YouTubeDetailsScreen;