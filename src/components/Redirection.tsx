import React, { Component } from 'react';
import { WebView } from 'react-native-webview';

import { View, TouchableOpacity, Text } from 'react-native';

const Redirection = ({ route }) => {
  const { url } = route.params;
  return (
    <View style={{ flex: 1 }}>
      <WebView
        source={{
          uri: url,
        }}
        style={{ flex: 1 }}
      />
    </View>
  );
};

export default Redirection;
