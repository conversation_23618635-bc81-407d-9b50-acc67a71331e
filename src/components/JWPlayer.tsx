import React, { useRef } from 'react';
import { StatusBar } from 'react-native';
import Player from './Player';
import <PERSON>Container from './PlayerContainer';

const JWPlayer = (props) => {
  const playerRef = useRef([]);

  const onTime = (e) => {
    // var {position, duration} = e.nativeEvent;
    // eslint-disable-line
    // console.log('onTime was called with: ', position, duration);
  };

  const onFullScreen = () => {
    StatusBar.setHidden(true);
  };

  const onFullScreenExit = () => {
    StatusBar.setHidden(false);
  };

  const renderPlayer = () => {
    return (
      <Player
        ref={playerRef}
        style={{ flex: 1 }}
        config={{
          autostart: false,
          playlist: [
            {
              file: 'https://cdn.jwplayer.com/manifests/' + props.videoID + '.m3u8',
              image: props.image,
            },
          ],
          styling: {
            colors: {},
          },
        }}
        onTime={onTime}
        onFullScreen={onFullScreen}
        onFullScreenExit={onFullScreenExit}
      />
    );
  };

  return <PlayerContainer children={renderPlayer()} />;
};
export default JWPlayer;
