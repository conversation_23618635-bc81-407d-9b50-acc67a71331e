import React, { useState } from 'react';
import { FlatList, StyleSheet, View, Text, Dimensions, TouchableOpacity } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import { useNavigation } from '@react-navigation/native';
import { useEffect } from 'react';
import { ActivityIndicator } from 'react-native';
import { Image } from 'react-native-elements';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { VideoPlayerService } from '../services/VideoPlayerService';
import { PlaylistItem } from './FullLandscapeListImageViewDouble';


const { width } = Dimensions.get('window');

const DropdownComponent = (props) => {
  const data = props.data;
  const navigation = useNavigation();
  const colors = useSelector((state: RootState) => state.homeScreen.colors);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const videoInfo = useSelector((state: RootState) => state.homeScreen.videoCloud);
  const [jwData, setJwData] = useState<unknown[]>([]);
  const [currentitem, setcurrentitem] = useState(null);
  const [youtubeData, setYoutubeData] = useState<unknown[]>([]);
  const [isYoutubeVideo, setIsYoutubeVideo] = useState(false);
  const playlistInfo: PlaylistItem[] = [];


  useEffect(() => {
    initVideoInfo(props.data.playlistId);
    setcurrentitem(data[0].itemRef);
  }, []);

  const initVideoInfo = async (playlistId: string) => {
    try {
      if (!playlistId) {
        return;
      }

      if (videoInfo[0].cloudName === 'YouTube') {
        const videoData = await VideoPlayerService.fetchYoutubePlaylist(playlistId);
        setYoutubeData(videoData);
        setIsYoutubeVideo(true);
      } else {
        const videoData = await VideoPlayerService.fetchJwplayerPlaylist(playlistId);
        setJwData(videoData);
      }
    } catch (error) {
      console.error('Error fetching playlist [DropdownComponent]:', error);
    }
  }

  const renderItem = (item: any) => {
    return item.itemRef != currentitem ? (
      <View style={styles(colors, isRTL).item}>
        <Text style={styles(colors, isRTL).textItem}>{item.itemRef}</Text>
        {/* {item.value === value && (
            <AntDesign
              style={styles(colors,isRTL).icon}
              color={props.color.primaryTextColor}
              name="checkcircle"
              size={width / 30}
            />
          )} */}
      </View>
    ) : null;
  };


  function secondsToHms(d) {
    d = Number(d);
    const h = Math.floor(d / 3600);
    const m = Math.floor((d % 3600) / 60);
    const s = Math.floor((d % 3600) % 60);

    const hDisplay = h > 0 ? h + (h == 1 ? ' Hr ' : ' Hrs ') : '';
    const mDisplay = m > 0 ? m + (m == 1 ? ' Min ' : ' Mins ') : '';
    const sDisplay = s > 0 ? s + (s == 1 ? ' Sec' : ' Sec') : '';
    return hDisplay + mDisplay + sDisplay;
  }


  function setPlaylistData() {
    // if (props.datas != null) {
    //   props.datas.items.forEach((item, key) => {
    //     playlistInfo.push({
    //       imageUrl: imageUrl + (item.itemid.landscapeThumbnail != null ? item.itemid.landscapeThumbnail.imagekey : ""),
    //       data: item,
    //       pageType: item.itemid.pageType,
    //       itemnavigation: item.itemid.externalPage,
    //       urlname: item.itemid.urlName,
    //       tag: item.itemid.tag,
    //       mainHeader: item.itemid.mainHeader,
    //     });
    //   });
    // }

    if (jwData.playlist != null) {
      jwData.playlist.forEach((item, key) => {
        playlistInfo.push({
          imageUrl: isYoutubeVideo ? item.thumbnail : item.image,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          tag: item.MediaType,
          mainHeader: item.title,
        });
      });
    }

    if (youtubeData != null) {
      youtubeData.forEach((item, key) => {
        playlistInfo.push({
          imageUrl: item.thumbnail,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          tag: item.etag,
          mainHeader: item.title,
        });
      });
    }
  }


  return (
    console.log('[DropdownComponent] value: ', playlistInfo),
    setPlaylistData(),
    (
      <View style={{ marginTop: width / 60 }}>
        <View style={styles(colors, isRTL).container}>
          <Dropdown
            style={styles(colors, isRTL).dropdown}
            placeholderStyle={styles(colors, isRTL).placeholderStyle}
            selectedTextStyle={styles(colors, isRTL).selectedTextStyle}
            inputSearchStyle={styles(colors, isRTL).inputSearchStyle}
            iconStyle={styles(colors, isRTL).iconStyle}
            data={data}
            // search
            maxHeight={width / 2.7}
            labelField="itemRef"
            valueField="itemid"
            placeholder={currentitem}
            searchPlaceholder="Search..."
            value={playlistInfo}
            onChange={(item) => {
              initVideoInfo(item.itemid);
              setcurrentitem(item.itemRef);
            }}
            // renderLeftIcon={() => (
            //   <AntDesign style={styles(colors,isRTL).icon} color="black" name="Safety" size={20} />
            // )}
            renderItem={renderItem}
          />
        </View>
        <View style={styles(colors, isRTL).row}></View>
        <View style={styles(colors, isRTL).inputWrap}></View>
        <View style={{ marginBottom: width / 45 }}>
          <FlatList
            style={{
              marginTop: width / 50,
              borderColor: colors.primaryTextColor,
              // borderWidth: 1,
              borderLeftColor: 'transparent',
              borderRightColor: 'transparent',
              borderBottomColor: 'transparent',
            }}
            // columnWrapperStyle={styles(colors,isRTL).row}
            scrollEnabled={false}
            showsHorizontalScrollIndicator={false}
            data={playlistInfo ? playlistInfo.playlist : null}
            renderItem={({ item }) => (
              <TouchableOpacity
                onPress={() => {
                  navigation.navigate('FullScreen', {
                    itemurl: item.mediaid,
                    image: item.image,
                  });
                }}>
                <View
                  style={{
                    // height: width / 3.5,
                    borderColor: colors.primaryTextColor,
                    // borderWidth: 1,
                    borderLeftColor: 'transparent',
                    borderRightColor: 'transparent',
                    borderTopColor: 'transparent',
                  }}>
                  <View style={styles(colors, isRTL).row}>
                    <View>
                      <Image
                        PlaceholderContent={<ActivityIndicator />}
                        source={{ uri: item.image }}
                        style={{
                          width: width / 3,
                          height: width / 4.5,
                          borderWidth: 0,
                          borderColor: '#d35647',
                          borderRadius: width / 50,
                          // resizeMode:'contain',
                          margin: width / 30,
                          // marginTop:5
                        }}
                      />
                    </View>
                    <View>
                      <Text numberOfLines={1} style={styles(colors, isRTL).text1}>
                        {item.title}
                      </Text>
                      <Text numberOfLines={5} style={styles(colors, isRTL).text2}>
                        {secondsToHms(item.duration)}
                      </Text>
                    </View>
                  </View>
                  <Text style={styles(colors, isRTL).text3}>{item.description}</Text>
                </View>
              </TouchableOpacity>
            )}
          />
        </View>
      </View>
    )
  );
};

export default DropdownComponent;


const styles = (colors, isRTL: boolean) => StyleSheet.create({
  dropdown: {
    height: width / 12,
    borderColor: colors.primaryTextColor,
    borderWidth: 1,
    borderRadius: 5,
    paddingHorizontal: width / 20,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: 'transparent',
  },
  icon: {
    marginRight: 5,
  },
  item: {
    padding: width / 50,
    //   flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: width / 12,
    backgroundColor: colors.primaryBackgroundColor,
  },
  textItem: {
    flex: 1,
    fontSize: width / 30,
    color: colors.primaryTextColor,
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
  },
  placeholderStyle: {
    fontSize: width / 30,
    color: colors.primaryTextColor,
    left: width / 25,
  },
  selectedTextStyle: {
    fontSize: width / 30,
    marginHorizontal: width / 25,
    color: colors.primaryTextColor,
  },
  iconStyle: {
    width: width / 30,
    height: width / 30,
  },
  inputSearchStyle: {
    height: width / 15,
    fontSize: width / 30,
    backgroundColor: colors.primaryBackgroundColor,
  },
  container: {
    backgroundColor: colors.primaryBackgroundColor,
    borderRadius: 5,
    width: width / 3,
    marginLeft: width / 30,
  },
  row: {
    flex: 1,
    flexDirection: 'row',
    width: width / 1.9,
    alignItems: 'center',
    justifyContent: isRTL ? 'flex-end' : 'flex-start'
  },
  inputWrap: {
    flex: 1,
    borderColor: colors.primaryTextColor,
  },
  text0: {
    color: colors.primaryTextColor,
    fontSize: width / 25,
    fontWeight: 'bold',
    marginHorizontal: width / 40,
  },
  text1: {
    color: colors.primaryTextColor,
    fontSize: width / 30,
    fontWeight: 'bold',
    marginHorizontal: width / 50,
    marginBottom: width / 50,
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
  },
  text2: {
    color: colors.primaryTextColor,
    fontSize: width / 35,
    // fontWeight: 'bold',
    marginHorizontal: width / 50,
    marginBottom: width / 100,
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
  },
  text3: {
    color: colors.primaryTextColor,
    fontSize: width / 35,
    // fontWeight: 'bold',
    marginHorizontal: width / 30,
    marginBottom: width / 100,
    width: width / 1.1,
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
  },
});
