import React, { forwardRef } from 'react';
import { Platform } from 'react-native';
import JWPlayer from '@jwplayer/jwplayer-react-native';

const PlayerComponent = forwardRef((props, ref) => {
  const { onLayout, tag, config, style } = props;

  const newProps = Object.assign({}, props);
  delete newProps.ref;
  delete newProps.key;
  delete newProps.config;
  delete newProps.style;

  return (
    <JWPlayer
      onLayout={onLayout}
      ref={ref}
      key={tag}
      style={[{ flex: 1 }, style]}
      config={{
        license: Platform.select({
          ios: '2jCzlpSG0LDVV/0IeFqXLnNfBrbudsCRk5n851WRqR936ddHLFstVqKmcP8=',
          android: 'q7he7udwE8Ewc9qMCTLE7mw86Vb5MTLOM7e63TCd8ZPN7P3eR5UxVIlNN/g=',
        }),
        backgroundAudioEnabled: true,
        styling: {
          colors: {},
        },
        ...config,
      }}
      {...newProps}
      onPlayerError={(e) => alert(e.nativeEvent?.error || 'Player Error.')}
    />
  );
});

PlayerComponent.displayName = 'PlayerComponent';

export default PlayerComponent;
