import React, { Component, useEffect, useState } from 'react';
import axios from 'axios';
import {
  FlatList,
  Text,
  Dimensions,
  View,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Linking,
} from 'react-native';
import { Card } from 'react-native-elements';
import { useNavigation } from '@react-navigation/native';
import { ActivityIndicator } from 'react-native';
import { Image } from 'react-native-elements';
import AdBanner from './AdBanner';
import PortraitList from './PortraitList';
import LandscapeList from './LandscapeList';
import Banner from './Banner';
import MovieCarouselList from './MovieCarousalList';
import SquareList from './SquareList';
import NewsLandscape from './NewsLandScape';
import NewArticleFullImage from './NewArticleFullImage';
import BigPortraitList from './BigPortraitList';
import ThreedCarousal from './ThreeDCarousel';
import ThreedLandscapeList from './ThreeDLandscapeList';
import FullLandscapeListImageView from './FullLandscapeListImageView';
import FullLandscapeListImageViewdouble from './FullLandscapeListImageViewDouble';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { AppService } from '../services/AppService';

const { width } = Dimensions.get('window');

const TabListLandscape = (props) => {
  const navigation = useNavigation();
  const color = useSelector((state: RootState) => state.homeScreen.colors);
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const [data, setData] = useState([]);
  const FullData = [];
  const [itemindex, setitemindex] = useState(0);
  const getdata = async () => {
    const token = await getToken();
    try {
      const result = await axios.get(
        AppService.baseUrl + '/api/view/' + props.datas._id + '/getTabFullDetail',
        {
          headers: { Authorization: token },
        }
      );
      console.log('sdvdvfdvdfvwdcvh', result.data.items);
      setData(result.data.items);
    } catch (e) {
      console.log(e);
      throw e;
    }
  };

  useEffect(() => {
    getdata();
    console.log('hcvfgdhjcgjsvxc', props.datas);
  }, []);

  const styles = StyleSheet.create({
    row: {
      flex: 1,
      flexDirection: 'row',
      width: width / 1.9,
      marginBottom: width / 50,
    },
    inputWrap: {
      flex: 1,
      borderColor: color.primaryTextColor,
    },
    text0: {
      color: color.primaryTextColor,
      fontSize: width / 25,
      fontWeight: 'bold',
      left: width / 40,
    },
    text1: {
      color: color.primaryTextColor,
      fontSize: width / 30,
      fontWeight: 'bold',
      left: width / 50,
      // marginBottom: width / 100,
      marginTop: width / 100,
    },
    text2: {
      color: color.primaryTextColor,
      fontSize: width / 34,
      marginLeft: width / 50,
      marginRight: width / 50,
    },
  });

  function navigationlist(pageType, itemnavigation, itemdata, urlname) {
    if (pageType == 'externalPage') {
      return Linking.openURL(itemnavigation);
    } else if (pageType == 'articlePage') {
      return navigation.push('ArticleDetails', {
        item: itemdata,
        color: props.color,
      });
    } else if (pageType == 'seasonPage') {
      return navigation.push('Season', {
        item: itemdata,
        color: props.color,
      });
    } else if (pageType == 'videoPage') {
      return navigation.push('Details', {
        item: itemdata,
        color: props.color,
      });
    } else if (pageType == 'normalPage') {
      return navigation.push('MoreDetail', {
        itemurlname: urlname,
        color: props.color,
      });
    }
  }

  // function datas() {
  //   {
  //     if (data != null) {
  //       data.map((item, key) => {
  //         FullData.push({
  //           imageUrl:
  //             constant.imageurl + item.itemid.bannerImage
  //             .imagekey,
  //           data: item,
  //           pageType: item.itemid.pageType,
  //           itemnavigation: item.itemid.externalPage,
  //           urlname: item.itemid.urlName,
  //           displayname: item.itemid.displayName,
  //           introduction: item.itemid.introduction,
  //         });
  //       });
  //     }
  //   if (JwData.playlist != null) {
  //     JwData.playlist.map((item, key) => {
  //       FullData.push({
  //         imageUrl: item.image,
  //         data: item,
  //         pageType: 'videoPage',
  //         itemnavigation: '',
  //         urlname: '',
  //         displayname: item.title,
  //         introduction: item.description,
  //       });
  //     });
  //   }

  //   }
  // }

  function componentlist() {
    {
      data.map((item, key) => {
        if (item.itemid != null) {
          if (
            item.itemid.elementType == 'portraitSlider' &&
            item.itemid.devices[2].itemName == 'iOS' &&
            item.itemid.devices[2].itemVisibility == true
          ) {
            FullData.push({
              compname: PortraitList,
              seemorecomp: 'FullPortraitList',
              listtitle: item.itemid.displayName,
              seemoretext: item.itemid.viewAllText,
              seemoretextvis: item.itemid.viewAllVisibility,
              datas: item.itemid,
            });
          } else if (
            item.itemid.elementType == 'landscapeSlider' &&
            item.itemid.devices[2].itemName == 'iOS' &&
            item.itemid.devices[2].itemVisibility == true
          ) {
            FullData.push({
              compname: LandscapeList,
              seemorecomp: 'FullLandscapeList',
              listtitle: item.itemid.displayName,
              seemoretext: item.itemid.viewAllText,
              seemoretextvis: item.itemid.viewAllVisibility,
              datas: item.itemid,
            });
          } else if (
            item.itemid.elementType == 'squareSlider' &&
            item.itemid.devices[2].itemName == 'iOS' &&
            item.itemid.devices[2].itemVisibility == true
          ) {
            FullData.push({
              compname: SquareList,
              seemorecomp: 'FullSquareList',
              listtitle: item.itemid.displayName,
              seemoretext: item.itemid.viewAllText,
              seemoretextvis: item.itemid.viewAllVisibility,
              datas: item.itemid,
            });
          } else if (
            item.itemid.elementType == 'articleList' &&
            item.itemid.devices[2].itemName == 'iOS' &&
            item.itemid.devices[2].itemVisibility == true
          ) {
            FullData.push({
              compname: NewsLandscape,
              seemorecomp: 'FullListPortrait',
              listtitle: item.itemid.displayName,
              seemoretext: item.itemid.viewAllText,
              seemoretextvis: item.itemid.viewAllVisibility,
              datas: item.itemid,
            });
          } else if (
            item.itemid.elementType == 'newsList' &&
            item.itemid.devices[2].itemName == 'iOS' &&
            item.itemid.devices[2].itemVisibility == true
          ) {
            FullData.push({
              compname: NewArticleFullImage,
              seemorecomp: 'LandscapeListDec',
              listtitle: item.itemid.displayName,
              seemoretext: item.itemid.viewAllText,
              seemoretextvis: item.itemid.viewAllVisibility,
              datas: item.itemid,
            });
          } else if (
            item.itemid.elementType == 'bigportraitSlider' &&
            item.itemid.devices[2].itemName == 'iOS' &&
            item.itemid.devices[2].itemVisibility == true
          ) {
            FullData.push({
              compname: BigPortraitList,
              seemorecomp: 'LandscapeListDec',
              listtitle: item.itemid.displayName,
              seemoretext: item.itemid.viewAllText,
              seemoretextvis: item.itemid.viewAllVisibility,
              datas: item.itemid,
            });
          } else if (
            item.itemid.elementType == 'newsSlider' &&
            item.itemid.devices[2].itemName == 'iOS' &&
            item.itemid.devices[2].itemVisibility == true
          ) {
            FullData.push({
              compname: NewsLandscape,
              seemorecomp: 'LandscapeListDec',
              listtitle: item.itemid.displayName,
              seemoretext: item.itemid.viewAllText,
              seemoretextvis: item.itemid.viewAllVisibility,
              datas: item.itemid,
            });
          } else if (
            item.itemid.elementType == '3dCarousel' &&
            item.itemid.devices[2].itemName == 'iOS' &&
            item.itemid.devices[2].itemVisibility == true
          ) {
            FullData.push({
              compname: ThreedCarousal,
              seemorecomp: 'LandscapeListDec',
              listtitle: item.itemid.displayName,
              seemoretext: item.itemid.viewAllText,
              seemoretextvis: item.itemid.viewAllVisibility,
              datas: item.itemid,
            });
          } else if (
            item.itemid.elementType == 'multiCarousel' &&
            item.itemid.devices[2].itemName == 'iOS' &&
            item.itemid.devices[2].itemVisibility == true
          ) {
            FullData.push({
              compname: ThreedLandscapeList,
              seemorecomp: 'LandscapeListDec',
              listtitle: item.itemid.displayName,
              seemoretext: item.itemid.viewAllText,
              seemoretextvis: item.itemid.viewAllVisibility,
              datas: item.itemid,
            });
          } else if (
            item.itemid.elementType == 'tabItem' &&
            item.itemid.devices[2].itemName == 'iOS' &&
            item.itemid.devices[2].itemVisibility == true
          ) {
            FullData.push({
              compname: TabListLandscape,
              seemorecomp: 'LandscapeListDec',
              listtitle: item.itemid.displayName,
              seemoretext: item.itemid.viewAllText,
              seemoretextvis: item.itemid.viewAllVisibility,
              datas: item.itemid,
            });
          } else if (
            item.itemid.elementType == 'grid' &&
            item.itemid.devices[2].itemName == 'iOS' &&
            item.itemid.devices[2].itemVisibility == true
          ) {
            FullData.push({
              compname: FullLandscapeListImageView,
              seemorecomp: 'LandscapeListDec',
              listtitle: item.itemid.displayName,
              seemoretext: item.itemid.viewAllText,
              seemoretextvis: item.itemid.viewAllVisibility,
              datas: item.itemid,
            });
          } else if (
            item.itemid.elementType == 'doubleGrid' &&
            item.itemid.devices[2].itemName == 'iOS' &&
            item.itemid.devices[2].itemVisibility == true
          ) {
            FullData.push({
              compname: FullLandscapeListImageViewdouble,
              seemorecomp: 'LandscapeListDec',
              listtitle: item.itemid.displayName,
              seemoretext: item.itemid.viewAllText,
              seemoretextvis: item.itemid.viewAllVisibility,
              datas: item.itemid,
            });
          }
        }
      });
    }
  }

  return (
    componentlist(),
    console.log('ggssgsgsgsg', FullData),
    (
      <View style={{ marginBottom: width / 25, marginTop: width / 60 }}>
        <View style={styles.row}>
          {/* <View style={styles.inputWrap}>
            <Text style={styles.text1}>{props.listtitle}</Text>
          </View> */}
          <View style={styles.inputWrap}>
            <FlatList
              style={{
                width: width / 1.02,
                // right: width / -20,
                alignContent: 'flex-end',
              }}
              scrollEnabled={false}
              showsHorizontalScrollIndicator={false}
              horizontal={true}
              data={FullData}
              renderItem={({ item, index }) => (
                <TouchableOpacity
                  style={{
                    width: width / 3.2,
                    alignContent: 'center',
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: width / 15,
                    marginLeft: width / 50,
                    marginBottom: width / 100,
                    backgroundColor:
                      itemindex == index ? color.primaryHighlightColor : 'transparent',
                    borderRadius:
                      props.appsettings !== undefined
                        ? props.appsettings.imageBorder === false
                          ? width / 50
                          : 0
                        : null,
                  }}
                  onPress={() => {
                    setitemindex(index);
                  }}>
                  <Text
                    style={{
                      fontWeight: 'bold',
                      fontSize: width / 34,
                      marginLeft: width / 50,
                      marginRight: width / 50,
                      textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',

                      color:
                        itemindex == index ? color.elementForegroundColor : color.primaryTextColor,
                    }}>
                    {item.listtitle}
                  </Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </View>

        <FlatList
          showsHorizontalScrollIndicator={false}
          data={FullData}
          renderItem={({ item, index }) =>
            index == itemindex ? (
              <item.compname
                color={color}
                seemorecomp={item.seemorecomp}
                listtitle={item.listtitle}
                seemoretext={item.seemoretext}
                seemoretextvis={item.seemoretextvis}
                url={item.url}
                urltype={item.urltype}
                datas={item.datas}
              />
            ) : null
          }
        />
      </View>
    )
  );
};

export default TabListLandscape;
