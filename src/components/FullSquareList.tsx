import React from 'react';
import { FlatList, Text, Dimensions, View, StyleSheet, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ActivityIndicator } from 'react-native';
import { Image } from 'react-native-elements';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';

const { width } = Dimensions.get('window');

const FullSquareList = (props) => {
  const navigation = useNavigation();
  const color = useSelector((state: RootState) => state.homeScreen.colors);
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const styles = StyleSheet.create({
    row: {
      flex: 1,
      justifyContent: 'space-around',
      direction: isRTL ? 'rtl' : 'ltr',
    },
    inputWrap: {
      flex: 1,
      alignSelf: isRTL ? 'flex-end' : 'flex-start',
      direction: isRTL ? 'rtl' : 'ltr',
      borderColor: color.primaryTextColor,
    },
    text1: {
      color: color.primaryTextColor,
      fontSize: width / 25,
      fontWeight: 'bold',
      left: width / 40,
      textAlign: isRTL ? 'right' : 'left',
      writingDirection: isRTL ? 'rtl' : 'ltr',
    },
    text2: {
      color: color.primaryTextColor,
      fontSize: width / 30,
      fontWeight: 'bold',
      textAlign: isRTL ? 'right' : 'left',
      writingDirection: isRTL ? 'rtl' : 'ltr',
      right: width / 40,
    },
  });

  return (
    <View style={{ marginBottom: width / 25, marginTop: width / 25 }}>
      <View style={styles.row}></View>
      <View style={styles.inputWrap}>
        <Text style={styles.text1}>{props.listtitle}</Text>
      </View>
      <View style={{ marginBottom: width / 45 }}>
        <FlatList
          style={{ marginTop: width / 50 }}
          numColumns={3} // set number of columns
          // columnWrapperStyle={styles.row}
          scrollEnabled={false}
          showsHorizontalScrollIndicator={false}
          data={props.datas}
          renderItem={({ item }) => (
            <TouchableOpacity
              onPress={() => {
                navigation.navigate('Details', {
                  itemId: item.title,
                  color: props.color,
                });
              }}>
              <Image
                PlaceholderContent={<ActivityIndicator />}
                source={{ uri: item.imageUrl }}
                style={{
                  width: width / 3.2,
                  height: width / 3.2,
                  borderWidth: 0,
                  borderColor: '#d35647',
                  borderRadius:
                    props.appsettings !== undefined
                      ? props.appsettings.imageBorder === false
                        ? width / 50
                        : 0
                      : null,
                  // resizeMode:'contain',
                  margin: width / 95,
                  // marginTop:10
                }}
              />
            </TouchableOpacity>
          )}
        />
      </View>
    </View>
  );
};

export default FullSquareList;
