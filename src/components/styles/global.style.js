import React from 'react';
import {StyleSheet, Dimensions, Platform} from 'react-native';

export const {width} = Dimensions.get('window');

export const globalStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    height: width / 1.77,
    width: width,
  },
  subContainer: {
    backgroundColor: 'black',
    alignItems: 'center',
    height: width / 1.77,
    width: width,
  },
  playerContainer: {
    height: width / 1.77,
    width: width,
  },
  player: {
    flex: 1,
  },
  text: {
    fontSize: 18,
    margin: 40,
  },
});

export const colors = {
  red: '#EC0041',
};
