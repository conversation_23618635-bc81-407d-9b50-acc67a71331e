import React, { useMemo } from 'react';
import {
  FlatList,
  Text,
  Dimensions,
  View,
  StyleSheet,
  TouchableOpacity,
  Linking,
  ActivityIndicator,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';

const { width } = Dimensions.get('window');

interface ItemData {
  imageUrl: string;
  data: any;
  pageType: string;
  itemnavigation: string;
  urlname: string;
  displayname: string;
  introduction: string;
}

interface Props {
  datas: {
    tab1Name: string;
    tab2Name: string;
    tab3Name: string;
    items: any[];
  };
  appsettings?: { imageBorder?: boolean };
  color: string;
}

const HightLightTextGrid: React.FC<Props> = ({ datas, appsettings, color }) => {
  const navigation = useNavigation();
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);

  // Process data efficiently using useMemo
  const { FirstData, SecondData, ThirdData } = useMemo(() => {
    const first: ItemData[] = [];
    const second: ItemData[] = [];
    const third: ItemData[] = [];

    if (datas?.items) {
      datas.items.forEach((item) => {
        const commonData: ItemData = {
          imageUrl: imageUrl + (item.itemid?.bannerImage?.imagekey || ''),
          data: item,
          pageType: item.itemid?.pageType || '',
          itemnavigation: item.itemid?.externalPage || '',
          urlname: item.itemid?.urlName || '',
          displayname: item.itemid?.displayName || '',
          introduction: item.itemid?.introduction || '',
        };

        console.log(" data[0]---------- ", commonData);

        switch (item.itemType) {
          case 'First Section':
            first.push(commonData);
            break;
          case 'Second Section':
            second.push(commonData);
            break;
          case 'Third Section':
            third.push(commonData);
            break;
        }
      });
    }

    return { FirstData: first, SecondData: second, ThirdData: third };
  }, [datas, imageUrl]);

  const navigateTo = (pageType: string, itemnavigation: string, itemdata: any, urlname: string) => {
    switch (pageType) {
      case 'externalPage':
        Linking.openURL(itemnavigation);
        break;
      case 'articlePage':
        navigation.navigate('ArticleDetails', { item: itemdata, color });
        break;
      case 'seasonPage':
        navigation.navigate('Season', { item: itemdata, color });
        break;
      case 'videoPage':
        navigation.navigate('Details', { item: itemdata, color });
        break;
      case 'normalPage':
        navigation.navigate('MoreDetail', { itemurlname: urlname, color });
        break;
      default:
        navigation.navigate('pagenotfound', { color });
    }
  };

  const renderSection = (sectionName: string, data: ItemData[]) => (
    <View>
      <View style={styles(isRTL).inputWrap}>
        {!isRTL && (<View style={styles(isRTL).indicator} />)}
        <Text style={styles(isRTL).text0}>{sectionName}</Text>
        {isRTL && (<View style={styles(isRTL).indicator} />)}
      </View>
      {data.length > 0 && (
        <TouchableOpacity
          onPress={() =>
            navigateTo(data[0].pageType, data[0].itemnavigation, data[0].data, data[0].urlname)
          }>
          <View style={styles(isRTL).imageContainer}>
            {data[0].imageUrl ? (
              <Image
                PlaceholderContent={<ActivityIndicator />}
                source={{ uri: data[0].imageUrl }}
                style={styles(isRTL).image}
                resizeMode='stretch'
              />
            ) : null}
            {data[0].data.itemid?.tag && (
              <View style={styles(isRTL).textview}>
                <Text style={styles(isRTL).text4}>{data[0].data.itemid.tag}</Text>
              </View>
            )}
            <Text style={styles(isRTL).text3}>{data[0].data.itemid?.mainHeader}</Text>
          </View>
        </TouchableOpacity>
      )}
      <FlatList
        style={{ marginTop: width / 50 }}
        scrollEnabled={false}
        showsHorizontalScrollIndicator={false}
        data={data.slice(1)}
        renderItem={({ item }) => (
          <TouchableOpacity
            onPress={() => navigateTo(item.pageType, item.itemnavigation, item.data, item.urlname)}>
            <View style={styles(isRTL).row}>
              <View style={styles(isRTL).smallIndicator} />
              <Text numberOfLines={6} style={styles(isRTL).text1}>
                {item.data.itemid?.mainHeader}
              </Text>
            </View>
          </TouchableOpacity>
        )}
      />
    </View>
  );

  return (
    <View style={styles(isRTL).container}>
      {renderSection(datas.tab1Name, FirstData)}
      {renderSection(datas.tab2Name, SecondData)}
      {renderSection(datas.tab3Name, ThirdData)}
    </View>
  );
};

export const styles = (isRTL: boolean) => StyleSheet.create({
  container: {
    marginBottom: width / 25,
    marginTop: width / 60,
    width: width,
  },
  row: {
    flexDirection: 'row',
    width: width / 1.05,
    marginLeft: width / 50,
    borderBottomColor: '#D3D3D3',
    borderColor: 'transparent',
    borderWidth: 1,
    marginBottom: width / 50,
    alignItems: isRTL ? 'flex-end' : 'flex-start',
  },
  inputWrap: {
    flexDirection: 'row',
    alignItems: isRTL ? 'flex-end' : 'flex-start',
  },
  text0: {
    flex: 1,
    fontSize: width / 25,
    fontWeight: 'bold',
    marginHorizontal: width / 40,
    marginBottom: width / 60,
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
  },
  text1: {
    fontSize: width / 30,
    fontWeight: 'bold',
    marginHorizontal: width / 50,
    marginBottom: width / 50,
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
  },
  text3: {
    fontSize: width / 30,
    position: 'absolute',
    fontWeight: 'bold',
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
    bottom: width / 100,
  },
  text4: {
    fontWeight: 'bold',
    fontSize: width / 34,
    textTransform: 'uppercase',
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
  },
  textview: {
    position: 'absolute',
    backgroundColor: '#ccc',
    borderTopLeftRadius: width / 50,
    borderWidth: 0,
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
  },
  imageContainer: {
    borderRadius: width / 50,
    // margin: width / 150,
    marginBottom: width / 50,
  },
  image: {
    width: width - 16,
    marginHorizontal: 8,
    height: width / 2,
    borderWidth: 0,
    borderColor: '#d35647',
    borderRadius: width / 50,
  },
  indicator: {
    backgroundColor: '#ccc',
    width: width / 70,
    height: 22,
    marginLeft: isRTL ? 0 : width / 50,
    marginRight: isRTL ? width / 50 : 0,
    marginBottom: width / 60,
  },
  smallIndicator: {
    backgroundColor: '#ccc',
    width: width / 70,
    height: width / 70,
    marginTop: width / 60,
  },
});

export default HightLightTextGrid;
