import React from 'react';
import { View, Text } from 'react-native';

/* styles */
import { globalStyles } from './styles/global.style';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';

export default ({ children, text }) => {
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  return (
    <View style={globalStyles.container}>
      <View style={globalStyles.subContainer}>
        <View style={globalStyles.playerContainer}>{children}</View>
      </View>
      <Text style={[globalStyles.text, {
        textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
      }]}>{text}</Text>
    </View>
  );
};
