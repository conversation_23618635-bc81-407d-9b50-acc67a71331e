import React, { useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Linking,
  ActivityIndicator,
} from 'react-native';
import Swiper from 'react-native-swiper';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { VideoPlayerService } from '../services/VideoPlayerService';
import { Image } from 'react-native-elements';
import { JwPlaylistItem } from './BigPortraitList';


interface BannerProps {
  datas: {
    playlistId?: string;
    items: {
      itemid: {
        bannerImage: { imagekey: string };
        pageType: string;
        externalPage: string;
        urlName: string;
        devices?: { itemName: string; itemVisibility: boolean }[];
      };
    }[];
  };
  color: { primaryBackgroundColor: string };
}

interface FullDataItem {
  imageUrl: string;
  data: any;
  pageType: string;
  itemnavigation: string;
  urlname: string;
}

const Banner: React.FC<BannerProps> = ({ datas, color }) => {
  const navigation = useNavigation();
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);
  const videoInfo = useSelector((state: RootState) => state.homeScreen.videoCloud);
  const isRTL = useSelector((state: RootState) => state.homeScreen.isRTL);
  const [jwData, setJwData] = useState<{ playlist?: { image: string }[] }>({});
  const [fullData, setFullData] = useState<FullDataItem[]>([]);
  const [youtubeData, setYoutubeData] = useState<unknown[]>([]);
  const [isYoutubeVideo, setIsYoutubeVideo] = useState(false);


  useEffect(() => {
    initVideoInfo();
  }, []);

  const initVideoInfo = async () => {
    try {
      if (!datas.playlistId) {
        return;
      }
      if (videoInfo[0].cloudName === 'YouTube') {
        const videoData = await VideoPlayerService.fetchYoutubePlaylist(datas.playlistId);
        setYoutubeData(videoData);
        setIsYoutubeVideo(true);
      } else {
        const videoData = await VideoPlayerService.fetchJwplayerPlaylist(datas.playlistId) as JwPlaylistItem[];
        setJwData(videoData);
      }
    } catch (error) {
      console.error('Error fetching playlist [Banner]:', error);
    }
  }

  useEffect(() => {
    let newData: FullDataItem[] = [];
    if (datas?.items) {
      newData = datas.items.map((item) => ({
        imageUrl: `${imageUrl}${item.itemid.bannerImage.imagekey}`,
        data: item,
        pageType: item.itemid.pageType,
        itemnavigation: item.itemid.externalPage,
        urlname: item.itemid.urlName,
      }));
    }
    if (jwData?.playlist) {
      newData = [
        ...newData,
        ...jwData.playlist.map((item) => ({
          imageUrl: item.image,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',

        })),
      ];
    }
    if (youtubeData) {
      youtubeData.forEach((item, key) => {
        newData.push({
          imageUrl: item.thumbnail,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
        });
      });
    }
    setFullData(newData);
  }, [datas, jwData, imageUrl]);

  const navigateToPage = (
    pageType: string,
    itemnavigation: string,
    itemdata: any,
    urlname: string
  ) => {
    switch (pageType) {
      case 'externalPage':
        Linking.openURL(itemnavigation);
        break;
      case 'articlePage':
        navigation.push('ArticleDetails', { item: itemdata, color });
        break;
      case 'seasonPage':
        navigation.push('Season', { item: itemdata, color });
        break;
      case 'videoPage':
        if (isYoutubeVideo) {
          navigation.push('YouTubeDetailsScreen', { item: itemdata, color: props.color, });
        } else {
          navigation.push('Details', { item: itemdata, color: props.color, });
        }
        break;
      case 'normalPage':
        navigation.push('MoreDetail', { itemurlname: urlname, color });
        break;
      default:
        navigation.push('pagenotfound', { color });
    }
  };

  return (
    fullData && fullData.length > 0 &&
    (
      <Swiper style={styles.wrapper} height={width / 1.77} autoplay marginBottom={width / 45} >
        {fullData.map((item, key) => (
          <View key={key} style={[styles.slide1, { direction: isRTL ? 'rtl' : 'ltr' }]}>
            <TouchableOpacity
              onPress={() =>
                navigateToPage(item.pageType, item.itemnavigation, item.data, item.urlname)
              }>
              {item.imageUrl ? (
                <Image
                  PlaceholderContent={<ActivityIndicator />}
                  style={styles.image}
                  source={{ uri: item.imageUrl }}
                />
              ) : null}
            </TouchableOpacity>
          </View>
        ))}
      </Swiper>
    )
  );
};

export default Banner;

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  wrapper: {},
  slide1: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width,
    flex: 1,
    height: width / 1.77,
  },
});