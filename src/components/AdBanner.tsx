import React from 'react';
import { View, StyleSheet, Dimensions, TouchableOpacity, Linking, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  slide1: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

interface ItemId {
  internalPage?: string;
  externalPage?: string;
  bannerImage: {
    imagekey: string;
  };
}

interface Item {
  itemid: ItemId;
}

interface AdBannerProps {
  datas: {
    items: Item[];
  };
}

const AdBanner: React.FC<AdBannerProps> = ({ datas }) => {
  const navigation = useNavigation();
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);

  if (!datas?.items?.length || !datas.items[0]?.itemid) {
    return null; // Prevents errors if data is missing
  }

  const { itemid } = datas.items[0];
  console.log('AdBanner =>', itemid);

  return (
    <View style={{ marginTop: width / 25 }}>
      <View style={styles.slide1}>
        {itemid.internalPage ? (
          <TouchableOpacity
            onPress={() => {
              console.log(itemid);

              navigation.navigate('Redirection', {
                url: itemid.internalPage,
              });
            }
            }>
            <Image
              source={{ uri: imageUrl + itemid.bannerImage.imagekey }}
              style={{
                width: width / 1.05,
                height: width / 5,
                borderRadius: width / 50,
                margin: width / 150,
                marginBottom: width / 20,
              }}
            />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            onPress={() => {
              if (itemid.pageType === 'livePage') {
                navigation.push('LiveStreamScreen');
              } else if (itemid.externalPage) {
                Linking.openURL(itemid.externalPage);
              }
            }}
          >
            <Image
              source={{
                uri: `https://smottstorage.s3.us-east-2.amazonaws.com/${itemid.bannerImage.imagekey}`,
              }}
              style={{
                width: width / 1.05,
                height: width / 5,
                borderRadius: width / 50,
                margin: width / 150,
                marginBottom: width / 20,
              }}
            />
          </TouchableOpacity>
        )}
      </View>
    </View >
  );
};

export default AdBanner;
