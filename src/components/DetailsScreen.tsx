import * as React from 'react';
import {
  Text,
  Dimensions,
  View,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Share,
  TouchableOpacity,
} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/Ionicons';
import InPlayer from './InPlayer';
import { useState } from 'react';
import axios from 'axios';
import JWPlayer from './JWPlayer';
import { useNavigation, useFocusEffect } from '@react-navigation/native';

import Spinner from 'react-native-loading-spinner-overlay/lib';
import PortraitList from './PortraitList';
import RoundList from './RoundList';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppService } from '../services/AppService';

const { width } = Dimensions.get('window');

const DetailsScreen = ({ route }) => {
  const navigation = useNavigation();
  const { item, color } = route.params;
  const colors = useSelector((state: RootState) => state.homeScreen.colors);
  console.log('Details Screen =>', item, color);
  const [JwData, setJwData] = useState([]);
  const [JwDataDetails, setJwDataDetails] = useState([]);
  const [ListID, setListID] = useState();
  const [SelectedMenu, setSelectedMenu] = useState(0);
  const [spinner, setspinner] = useState(true);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);

  // const timer = setTimeout(() => {
  //   setspinner(false);
  // }, 2600);

  const getdata = async () => {
    const listID = item.itemid ? item.itemid.videoId : item.mediaid;
    if (!listID) {
      console.log("Invalid listID:", listID);
      return;
    }

    try {
      const { data } = await axios.get(`https://cdn.jwplayer.com/v2/media/${listID}`);

      if (!data.playlist || data.playlist.length === 0) {
        console.log("Invalid playlist data");
        return;
      }

      console.log("Media Type:", data.playlist[0].MediaType);

      setJwData(data);
      setJwDataDetails(data.playlist[0]);
      setSelectedMenu(data.playlist[0].MediaType === "Movie" ? 1 : 0);
    } catch (e) {
      console.error("Error fetching data:", e);
    } finally {
      setspinner(false);
    }
  };



  // useEffect(() => {
  //   JwDataDetails.length == 0 ? getdata() : null;
  // }, [getdata]);

  useFocusEffect(
    React.useCallback(() => {
      InPlayer.tokenStorage.overrides = {
        setItem: async function (key, value) {
          try {
            await AsyncStorage.setItem(key, value);
          } catch (error) {
            console.error('Error saving data', error);
          }
          console.log('Stored Value:', value);
        },
        getItem: async function (key) {
          try {
            return await AsyncStorage.getItem(key);
          } catch (error) {
            console.error('Error retrieving data', error);
          }
        },
        removeItem: async function (key) {
          try {
            await AsyncStorage.removeItem(key);
          } catch (error) {
            console.error('Error removing data', error);
          }
        },
      };

      if (JwDataDetails.length === 0) {
        getdata();
      }

      getItemValue();
    }, [JwDataDetails, getdata, getItemValue]) // Dependencies
  );

  const styles = StyleSheet.create({
    watchnowbuttoncontainer: {
      flexDirection: 'row',
      backgroundColor: colors.primaryHighlightColor,
      alignItems: 'center',
      justifyContent: 'center',
      marginLeft: width / 22,
      marginRight: width / 22,
      marginTop: 0,
      marginBottom: width / 40,
      width: width / 1.1,
      height: width / 12,
      borderRadius: 5,
    },
    watchbuttoncontainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginLeft: width / 22,
      marginRight: width / -44,
      marginTop: 0,
      marginBottom: width / 40,
      width: width / 2 / 1.13,
      height: width / 12,
      borderRadius: 5,
      borderWidth: 1,
      borderColor: colors.primaryTextColor,
    },
    subbutton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginLeft: width / 22,
      marginRight: width / -44,
      marginTop: 0,
      marginBottom: width / 40,
      width: width / 2.25,
      height: width / 12,
      borderRadius: 5,
      borderWidth: 1,
      borderColor: colors.primaryTextColor,
    },
    Relatedbutton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginLeft: width / 40,
      marginRight: width / -44,
      marginTop: 0,
      marginBottom: width / 40,
      width: width / 2.2,
      height: width / 12,
      borderWidth: SelectedMenu == 2 ? 3 : 0,
      borderColor: 'transparent',
      borderBottomColor: colors.primaryHighlightColor,
      borderRadius: 0,
      // backgroundColor: SelectedMenu == 2 ? color.primaryHighlightColor : null,
    },
    Detailsbutton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginLeft: width / 22,
      marginRight: width / -44,
      marginTop: 0,
      marginBottom: width / 40,
      width: width / 2.2,
      height: width / 12,
      borderWidth: SelectedMenu == 1 ? 3 : 0,
      borderColor: 'transparent',
      borderBottomColor: colors.primaryHighlightColor,
      borderRadius: 0,
      // backgroundColor: SelectedMenu == 1 ? color.primaryHighlightColor : null,
    },
  });

  const onShare = async () => {
    try {
      const result = await Share.share({
        message:
          'http://smottapp.com/share/index.php?type=season&title=' +
          JwData.title +
          '&appid=' + AppService.appId + '&id=' +
          item._id +
          '&image=' +
          JwDataDetails.image +
          '&url=http://smottapp.com:3002/' + AppService.appId + '/' +
          JwData.title,
      });
      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // shared with activity type of result.activityType
        } else {
          // shared
        }
      } else if (result.action === Share.dismissedAction) {
        // dismissed
      }
    } catch (error) {
      alert(error.message);
    }
  };

  function secondsToHms(d) {
    d = Number(d);
    const h = Math.floor(d / 3600);
    const m = Math.floor((d % 3600) / 60);
    const s = Math.floor((d % 3600) % 60);

    const hDisplay = h > 0 ? h + (h == 1 ? ' Hr ' : ' Hrs ') : '';
    const mDisplay = m > 0 ? m + (m == 1 ? ' Min ' : ' Mins ') : '';
    const sDisplay = s > 0 ? s + (s == 1 ? ' Sec' : ' Sec') : '';
    return hDisplay + mDisplay + sDisplay;
  }

  async function getItemValue(key) {
    try {
      const value = await AsyncStorage.getItem('inplayer_token');
      setinplayertoken(JSON.parse(value));
      // console.log('fgfggggfgfgfgf', inplayertoken);
      return true;
    } catch (exception) {
      return false;
    }
  }

  async function updateprofile() {
    const setting = {};
    const watchHistory = [];
    const profile = {};
    console.log('sgdffuy', InPlayer.Account.getToken());
    const favourites = [
      {
        videoId: '6242e2cd615fc3443432c859',
        title: 'Better Half',
        type: 'page',
        pageType: 'seasonPage',
        imageUrl:
          'https://smottstorage.s3.us-east-2.amazonaws.com/2ab107bda405aabc86a5723abffa3f93.jpg',
        urlName: 'betterhalf',
        date: 'Sat 24 Sep 2022',
      },
    ];

    InPlayer.Account.updateAccount(
      {
        metadata: { favourites: JSON.stringify(favourites) },
      },
      InPlayer.Account.token
    )
      .then((data) => console.log(data))
      .catch((error) => error.response.json().then((data) => console.log('Error', data)));
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.primaryBackgroundColor }}>
      <Spinner
        visible={spinner}
        textContent={'Loading...'}
        textStyle={{ color: colors.primaryHighlightColor }}
        overlayColor={colors.primaryBackgroundColor}
        color={colors.primaryHighlightColor}
      />
      <ScrollView showsVerticalScrollIndicator={false} nestedScrollEnabled={true}>
        <View>
          {JwDataDetails.MediaType == 'Movie' ? (
            <JWPlayer videoID={JwDataDetails.TrailerId} image={JwDataDetails.image} />
          ) : (
            <JWPlayer videoID={JwDataDetails.mediaid} image={JwDataDetails.image} />
          )}
        </View>
        <Text
          style={{
            color: colors.primaryTextColor,
            margin: width / 22,
            fontWeight: 'bold',
            fontSize: width / 20,
            textAlign: isRTL ? 'right' : 'left',
            writingDirection: isRTL ? 'rtl' : 'ltr',
            marginTop: width / 25,
            marginBottom: width / 40,
          }}>
          {JwData.title}
        </Text>
        <View
          style={{
            marginLeft: width / 22,
            marginRight: width / 22,
            marginTop: 0,
            width: width / 1.1,
            marginBottom: 0,
          }}>
          <View style={{ flexDirection: 'row' }}>
            {JwDataDetails.PgRating != '' && JwDataDetails.PgRating != null ? (
              <Text
                style={{
                  marginLeft: 0,
                  fontSize: width / 38,
                  textAlign: isRTL ? 'right' : 'left',
                  writingDirection: isRTL ? 'rtl' : 'ltr',
                  marginTop: 1,
                  marginBottom: width / 25,
                  color: '#808080',
                }}>
                Rating : {JwDataDetails.PgRating}
              </Text>
            ) : null}
            {JwDataDetails.VIdeoDuration != '' && JwDataDetails.VIdeoDuration != null ? (
              <Text
                style={{
                  marginLeft: width / 38,
                  fontSize: width / 38,
                  textAlign: isRTL ? 'right' : 'left',
                  writingDirection: isRTL ? 'rtl' : 'ltr',
                  marginTop: 1,
                  marginBottom: width / 25,
                  color: '#808080',
                }}>
                {secondsToHms(JwDataDetails.duration)}
              </Text>
            ) : null}
            {JwDataDetails.ReleaseOn != '' && JwDataDetails.ReleaseOn != null ? (
              <Text
                style={{
                  marginLeft: width / 38,
                  fontSize: width / 38,
                  textAlign: isRTL ? 'right' : 'left',
                  writingDirection: isRTL ? 'rtl' : 'ltr',
                  marginTop: 1,
                  marginBottom: width / 25,
                  color: '#808080',
                }}>
                {JwDataDetails.ReleaseOn}
              </Text>
            ) : null}
            {JwDataDetails.MPAARating != '' && JwDataDetails.MPAARating != null ? (
              <Text
                style={{
                  marginLeft: width / 38,
                  fontSize: width / 38,
                  textAlign: isRTL ? 'right' : 'left',
                  writingDirection: isRTL ? 'rtl' : 'ltr',
                  marginTop: 0,
                  marginBottom: width / 25,
                  color: '#808080',
                  borderWidth: 1,
                  borderColor: '#808080',
                  borderRadius: 5,
                }}>
                {JwDataDetails.MPAARating}
              </Text>
            ) : null}
          </View>
          {JwDataDetails.Genre != '' && JwDataDetails.Genre != null ? (
            <Text
              style={{
                marginLeft: 0,
                fontSize: width / 38,
                textAlign: isRTL ? 'right' : 'left',
                writingDirection: isRTL ? 'rtl' : 'ltr',
                marginTop: width / -30,
                marginBottom: width / 25,
                color: '#808080',
              }}>
              {JwDataDetails.Genre}
            </Text>
          ) : null}
          {JwDataDetails.AudioTracks != '' && JwDataDetails.AudioTracks != null ? (
            <Text
              style={{
                marginLeft: 0,
                fontSize: width / 38,
                textAlign: isRTL ? 'right' : 'left',
                writingDirection: isRTL ? 'rtl' : 'ltr',
                marginTop: width / -30,
                marginBottom: width / 25,
                color: '#808080',
              }}>
              Audio : {JwDataDetails.AudioTracks}
            </Text>
          ) : null}
        </View>
        <View style={{ flexDirection: 'row' }}>
          {JwDataDetails.MediaType == 'Movie' ? (
            <TouchableOpacity
              onPress={() => {
                navigation.push('FullScreen', {
                  itemurl: JwDataDetails.MovieId,
                  image: JwDataDetails.image,
                  color: color,
                });
              }}
              style={styles.watchnowbuttoncontainer}>
              <View style={{ flexDirection: 'row' }}>
                <MaterialCommunityIcons
                  name="play"
                  color={colors.elementForegroundColor}
                  size={width / 26}
                />
                <Text
                  style={{
                    fontSize: width / 30,
                    color: colors.elementForegroundColor,
                    marginLeft: 5,
                  }}>
                  Watch Now
                </Text>
              </View>
            </TouchableOpacity>
          ) : null}
        </View>
        <View style={{ flexDirection: 'row' }}>
          <TouchableOpacity onPress={updateprofile} style={styles.subbutton}>
            <View style={{ flexDirection: 'row' }}>
              <MaterialCommunityIcons
                name="star"
                color={colors.primaryHighlightColor}
                size={width / 26}
              />
              <Text
                style={{
                  fontSize: width / 30,
                  color: colors.primaryHighlightColor,
                  marginLeft: 5,
                }}>
                {isRTL ? 'المفضلة' : 'Favourite'}
              </Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity onPress={onShare} style={styles.subbutton}>
            <View style={{ flexDirection: 'row' }}>
              <MaterialCommunityIcons
                name="share"
                color={colors.primaryTextColor}
                size={width / 25}
              />
              <Text
                style={{
                  fontSize: width / 30,
                  color: colors.primaryTextColor,
                  marginLeft: 5,
                }}>
                {isRTL ? 'مشاركة' : 'Share'}
              </Text>
            </View>
          </TouchableOpacity>
        </View>
        {SelectedMenu != 0 ? (
          <View style={{ flexDirection: 'row', marginTop: width / 30 }}>
            <TouchableOpacity
              onPress={() => {
                setSelectedMenu(1);
              }}
              style={styles.Detailsbutton}>
              <View style={{ flexDirection: 'row' }}>
                <Text
                  style={{
                    fontSize: width / 30,
                    color: colors.primaryTextColor,
                    marginLeft: 5,
                  }}>
                  {isRTL ? 'تفاصيل' : 'Details'}
                </Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setSelectedMenu(2);
              }}
              style={styles.Relatedbutton}>
              <View style={{ flexDirection: 'row' }}>
                <Text
                  style={{
                    fontSize: width / 30,
                    color: colors.primaryTextColor,
                    marginLeft: 5,
                  }}>
                  {isRTL ? 'ذو صلة' : 'Related'}
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        ) : null}
        <View style={{ flexDirection: 'row' }}></View>
        {SelectedMenu == 1 || SelectedMenu == 0 ? (
          <View>
            {JwDataDetails.MediaType == 'Movie' ? (
              <Text
                style={{
                  color: colors.primaryTextColor,
                  fontSize: width / 30,
                  fontWeight: 'bold',
                  textAlign: isRTL ? 'right' : 'left',
                  writingDirection: isRTL ? 'rtl' : 'ltr',
                  marginHorizontal: width / 40,
                }}>
                {isRTL ? 'ملخص' : 'Synopsis'}
              </Text>
            ) : null}
            <Text
              style={{
                margin: width / 22,
                fontSize: width / 32,
                textAlign: isRTL ? 'right' : 'left',
                writingDirection: isRTL ? 'rtl' : 'ltr',
                marginTop: width / 30,
                marginBottom: width / 25,
                color: colors.primaryTextColor,
              }}>
              {JwData.description}
            </Text>
            {JwDataDetails.MediaType == 'Movie' ? (
              <RoundList
                color={color}
                seemorecomp={''}
                listtitle={'CAST & CREW'}
                seemoretext={''}
                seemoretextvis={false}
                url={''}
                urltype={''}
                datas={JwDataDetails}
              />
            ) : null}
          </View>
        ) : null}
        {SelectedMenu == 2 || SelectedMenu == 0 ? (
          JwDataDetails.Recommendation != '' ? (
            <PortraitList
              color={colors}
              seemorecomp={''}
              listtitle={''}
              seemoretext={''}
              seemoretextvis={false}
              url={''}
              urltype={''}
              playlistId={JwDataDetails.Recommendation}
              datas={null}
            />
          ) : null
        ) : null}
      </ScrollView>
    </SafeAreaView>
  );
};

export default DetailsScreen;
