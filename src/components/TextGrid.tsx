import React, { useEffect, useState } from 'react';
import {
  FlatList,
  Text,
  Dimensions,
  View,
  StyleSheet,
  TouchableOpacity,
  Linking,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';

const { width } = Dimensions.get('window');

const TextGrid = (props) => {
  const navigation = useNavigation();
  const colors = useSelector((state: RootState) => state.homeScreen.colors);
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);

  const [sections, setSections] = useState({
    first: [],
    second: [],
    third: [],
  });

  const styles = StyleSheet.create({
    row: {
      flexDirection: 'row',
      width: width / 1.05,
      marginLeft: width / 50,
      borderBottomColor: '#D3D3D3',
      borderColor: 'transparent',
      borderWidth: 1,
      // marginBottom: width / 50,
      direction: isRTL ? 'rtl' : 'ltr'
    },
    inputWrap: {
      flex: 1,
      borderColor: colors.primaryTextColor,
      flexDirection: 'row',
      justifyContent: isRTL ? 'flex-end' : 'flex-start',
    },
    text0: {
      color: colors.primaryTextColor,
      fontSize: width / 25,
      fontWeight: 'bold',
      marginLeft: isRTL ? 0 : width / 40,
      marginRight: isRTL ? width / 40 : 0,
      marginBottom: width / 60,
      width: 100,
      textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
    },
    text1: {
      color: colors.primaryTextColor,
      fontSize: width / 30,
      fontWeight: 'bold',
      marginBottom: width / 60,
      width: width / 1.1,
      marginHorizontal: 10,
      textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
    },
    text2: {
      color: colors.primaryTextColor,
      fontSize: width / 34,
      left: width / 50,
      marginBottom: width / 100,
      textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
    },
  });

  const getData = () => {
    try {
      if (props.datas && props.datas.items) {
        const sectionMap = {
          first: [],
          second: [],
          third: [],
        };
        props.datas.items.forEach((item) => {
          const commonData = {
            imageUrl: imageUrl + (item.itemid.landscapeThumbnail?.imagekey ?? ''),
            data: item,
            pageType: item.itemid.pageType,
            itemnavigation: item.itemid.externalPage,
            urlname: item.itemid.urlName,
            displayname: item.itemid.displayName,
            introduction: item.itemid.introduction,
          };
          if (item.itemType === 'First Section') {
            sectionMap.first.push(commonData);
          } else if (item.itemType === 'Second Section') {
            sectionMap.second.push(commonData);
          } else if (item.itemType === 'Third Section') {
            sectionMap.third.push(commonData);
          }
        });
        setSections(sectionMap);
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    getData();
  }, [props.datas]);

  const handleNavigation = (pageType, itemnavigation, itemdata, urlname) => {
    console.log("navigatin from textgrid-----> ", pageType);

    if (pageType === 'externalPage') {
      return Linking.openURL(itemnavigation);
    } else if (pageType === 'articlePage') {
      return navigation.push('ArticleDetails', { item: itemdata, color: props.color });
    } else if (pageType === 'seasonPage') {
      return navigation.push('Season', { item: itemdata, color: props.color });
    } else if (pageType === 'videoPage') {
      return navigation.push('Details', { item: itemdata, color: props.color });
    } else if (pageType === 'normalPage') {
      return navigation.push('MoreDetail', { itemurlname: urlname, color: props.color });
    } else if (pageType === "livePage") {
      console.log(itemdata);
      return navigation.push('LiveStreamScreen')
    } else {
      showAlert('Error', 'No data found');
    }
  };

  return (
    (sections.first.length > 0 || sections.second.length > 0 || sections.third.length > 0) &&
    (
      <View style={{}}>
        {['first', 'second', 'third'].map((sectionKey) => {
          const section = sections[sectionKey];
          let tabNameStr = props.datas[`tab${sectionKey.charAt(0).toUpperCase() + sectionKey.slice(1)}Name`] || '';
          switch (sectionKey) {
            case 'first':
              tabNameStr = props.datas[`tab1Name`] || '';
              break;
            case 'second':
              tabNameStr = props.datas[`tab2Name`] || '';
              break;
            case 'third':
              tabNameStr = props.datas[`tab3Name`] || '';
              break;

            default:
              break;
          }
          return (
            <View key={sectionKey} style={{}}>
              <View style={styles.inputWrap}>
                {!isRTL && tabNameStr != '' && (
                  <View
                    style={{
                      backgroundColor: colors.secondaryHighlightColor,
                      width: width / 70,
                      marginLeft: width / 50,
                      marginBottom: width / 60,
                    }} />
                )}
                <Text style={[styles.text0, {
                  textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
                }]}>
                  {tabNameStr}
                </Text>
                {isRTL && tabNameStr != '' && (
                  <View
                    style={{
                      backgroundColor: colors.secondaryHighlightColor,
                      width: width / 70,
                      marginRight: width / 50,
                      marginBottom: width / 60,
                    }} />
                )}
              </View>
              <FlatList
                style={{ marginBottom: width / 40, }}
                data={section}
                scrollEnabled={false}
                showsHorizontalScrollIndicator={false}
                keyExtractor={(item) => item.urlname}
                renderItem={({ item, index }) => (
                  <TouchableOpacity
                    onPress={() =>
                      handleNavigation(item.pageType, item.itemnavigation, item.data, item.urlname)
                    }>
                    <View
                      style={{
                        flexDirection: 'row',
                        borderBottomColor: index !== section.length - 1 ? '#D3D3D3' : 'transparent',
                        borderColor: 'transparent',
                        borderWidth: 1,
                      }}>
                      {!isRTL && (<View
                        style={{
                          backgroundColor: colors.secondaryHighlightColor,
                          width: width / 70,
                          height: width / 70,
                          marginTop: width / 70,
                          marginLeft: width / 60,
                        }} />)}

                      <Text numberOfLines={6} style={[styles.text1, {
                        textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
                      }]}>
                        {item.displayname || item.data.itemid.mainHeader}
                      </Text>

                      {isRTL && (<View
                        style={{
                          backgroundColor: colors.secondaryHighlightColor,
                          width: width / 70,
                          height: width / 70,
                          marginTop: width / 70,
                        }} />)}
                    </View>
                  </TouchableOpacity>
                )}
              />
            </View>
          );
        })}
      </View>
    )
  );
};

const showAlert = (title: string, content: string) => {
  Alert.alert(
    title,
    content,
    [{ text: 'OK', onPress: () => console.log('OK Pressed') }]
  );
};


export default TextGrid;
