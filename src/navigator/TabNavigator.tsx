import React from "react";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import HomeScreen from "../screens/HomeScreen";
import SettingsScreen from "../screens/SettingsScreen";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { Dimensions, Image } from "react-native";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "../redux/store"; // Adjust according to your store path
import { iconMap } from "../utils/constants";
import { setActiveTabState } from "../redux/MainScreenSlice";
import { TabActions, useNavigation, CommonActions } from "@react-navigation/native"


type MenuItem = {
  _id: string;
  menuTitle: string;
  urlName: string;
  menuType: string;
  menuPage: string;
  items: Array<{
    _id: string;
    menuPage: string;
    menuPageRef: string;
    menuPageType: string;
    menuUrlName: string;
    menuTitle: string;
    menuOrder: string;
    menuIcon: string;
  }>;
  externalUrl: string | null;
  internalUrl: string | null;
  menuIcon: string;
  menuOrder: string;
  application: string;
  date: string;
  __v: number;
};

const TabNavigator = () => {
  const Tab = createBottomTabNavigator();
  const { width } = Dimensions.get("window");
  const dispatch = useDispatch();
  const navigation = useNavigation();
  // Access Redux state with types
  const menu = useSelector((state: RootState) => state.homeScreen.menu);
  const isBottomTabActive = useSelector((state: RootState) => state.mainScreen.isBottomTabActive);
  const appSettings = useSelector(
    (state: RootState) => state.homeScreen.appSettings
  );
  const { colors, imageUrl, imageKey, isRTL } = useSelector((state: RootState) => state.homeScreen);


  console.log(menu);

  const hasHomeTab = menu.some(
    (item) => item.menuTitle == "Home"
  );
  return (
    <Tab.Navigator
      screenOptions={{
        headerTitle: () => (
          <Image
            style={{ width: width / 5, height: width / 17 }}
            source={{ uri: `${imageUrl}${imageKey}` }}
          />
        ),
        headerShown: false,
        headerTintColor: colors.primaryHighlightColor,
        tabBarActiveTintColor: isBottomTabActive ? colors.primaryHighlightColor : 'grey',
        headerStyle: {
        },
        tabBarStyle: { height: 60, backgroundColor: colors.menuBackgroundColor },
      }}
      screenListeners={({ route }) => ({ // listener receives navigation, route
        tabPress: () => {
          if (route.name == 'Home') {
            // route.params['urlname'] = 'home';
            // route.params['activeTopTabIndex'] = -1;
            // route.params['title'] = 'Home';
            navigation.dispatch(
              CommonActions.reset({
                index: 0,
                routes: [
                  {
                    name: "Home",
                    params: {
                      activeTopTabIndex: -1,
                      color: colors,
                      appsettinglist: appSettings,
                      title: "Home",
                      urlname: 'home',
                    },
                  },
                ],
              })
            );

          }
          dispatch(setActiveTabState(true));
        },
      })}
      tabBarOptions={{
        activeTintColor: isBottomTabActive ? colors.primaryHighlightColor : 'gray',
        inactiveTintColor: colors.primaryTextColor,
        activeBackgroundColor: colors.menuBackgroundColor,
        inactiveBackgroundColor: colors.menuBackgroundColor,
        labelStyle: { fontSize: width / 35 },
        iconStyle: {
          width: width / 20,
          height: width / 20,
        },
      }}
    >
      {/* Home Tab */}
      {!hasHomeTab && (<Tab.Screen
        name="Home"
        options={{
          tabBarLabel: isRTL ? 'الرئيسية' : "Home",
          tabBarIcon: ({ color }) => (
            <MaterialCommunityIcons
              name="home"
              color={color}
              size={width / 20}
            />
          ),
        }}

      >
        {() => (
          <HomeScreen
            color={colors}
            appsettinglist={appSettings}
            title={isRTL ? 'الرئيسية' : "Home"}
            urlname="home"
            activeTopTabIndex={-1}
          />
        )}
      </Tab.Screen>
      )}
      {/* Dynamic Menu Tabs */}
      {menu.map((item: MenuItem, index: number) => {
        console.log("menu.map((item: MenuItem, index: number) =>", item, index);
        const iconName = iconMap[item.menuIcon] || "home"; // Default to 'home' if no match
        return index <= 2 && index >= 1 ? (
          <Tab.Screen
            key={item.menuTitle || index}
            name={item.menuTitle}
            options={{
              tabBarLabel: item.menuTitle,
              tabBarIcon: ({ color }) => (
                <MaterialCommunityIcons
                  name={iconName}
                  color={color}
                  size={width / 20}
                />
              ),
            }}
          >
            {() => (
              <HomeScreen
                color={colors}
                appsettinglist={appSettings}
                title={item.menuTitle}
                urlname={item.urlName}
                activeTopTabIndex={-1}
              />
            )}
          </Tab.Screen>
        ) : null;
      })}

      {/* More Tab */}
      <Tab.Screen
        name="More"
        options={{
          tabBarLabel: isRTL ? 'المزيد' : "More",
          tabBarIcon: ({ color }) => (
            <MaterialCommunityIcons
              name="dots-vertical"
              color={color}
              size={width / 20}
            />
          ),
        }}
      >
        {(props) => (
          <SettingsScreen
            color={colors}
            appsettinglist={appSettings}
            title={isRTL ? 'المزيد' : "More"}
            nav={menu}
            {...props}
          />
        )}
      </Tab.Screen>
    </Tab.Navigator>
  );
};

export default TabNavigator;
