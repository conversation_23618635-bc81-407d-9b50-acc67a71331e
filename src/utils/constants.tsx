import { AppService } from "../services/AppService";

export const menus = [
  {
    _id: '64084c3d883eac56d425d7d0',
    menuTitle: 'Gulf',
    urlName: 'anjitha asok',
    menuType: 'multiple',
    menuPage: '640848a4883eac56d425d682',
    items: [
      {
        _id: '64084cb2883eac56d425d7df',
        menuPage: '63ec7c1361459c1a00881135',
        menuPageRef: 'PodcastnormalPage',
        menuPageType: 'normalPage',
        menuUrlName: 'podcast',
        menuTitle: 'News',
        menuOrder: '1',
        menuIcon: 'Dollar',
      },
      {
        _id: '64084cb2883eac56d425d7e0',
        menuPage: '6407217b883eac56d425cb94',
        menuPageRef: 'Arabian storiesnormalPage',
        menuPageType: 'normalPage',
        menuUrlName: 'Arabian storiesnormal',
        menuTitle: 'Arabian Stories',
        menuOrder: '2',
        menuIcon: 'Dollar',
      },
    ],
    externalUrl: null,
    internalUrl: null,
    menuIcon: 'Padlock',
    menuOrder: '4',
    application: AppService.appId,
    date: '2023-03-08T08:50:05.869Z',
    __v: 0,
  },
  {
    _id: '640724f9883eac56d425cc8e',
    menuTitle: 'Debate',
    urlName: 'Sportsnormalpage',
    menuType: 'multiple',
    menuPage: '6402f305883eac56d425c33f',
    items: [
      {
        _id: '64072554883eac56d425cca9',
        menuPage: '6407217b883eac56d425cb89',
        menuPageRef: 'super primetimenormalPage',
        menuPageType: 'normalPage',
        menuUrlName: 'superprimetimenormalpage',
        menuTitle: 'Super Prime Time',
        menuOrder: '1',
        menuIcon: 'Document',
      },
      {
        _id: '640833a8883eac56d425d1b4',
        menuPage: '64083359883eac56d425d192',
        menuPageRef: 'NammalariyanamnormalPage',
        menuPageType: 'normalPage',
        menuUrlName: 'Nammalariyanamnormalpage',
        menuTitle: 'Nammalariyanam',
        menuOrder: '2',
        menuIcon: 'Document',
      },
    ],
    externalUrl: null,
    internalUrl: null,
    menuIcon: 'Star',
    menuOrder: '3',
    application: AppService.appId,
    date: '2023-03-07T11:50:17.462Z',
    __v: 0,
  },
  {
    _id: '6405d03b883eac56d425c776',
    menuTitle: 'NEWS',
    urlName: null,
    menuType: 'multiple',
    menuPage: '6404c0a4fda8b74b18b64642',
    items: [
      {
        _id: '6405d04e883eac56d425c777',
        menuPage: '6404adc4c288ab517076fc1f',
        menuPageRef: 'keralanormalPage',
        menuPageType: 'normalPage',
        menuUrlName: 'kerala',
        menuTitle: 'Kerala',
        menuOrder: '1',
        menuIcon: 'Document',
      },
      {
        _id: '640824c1883eac56d425cda5',
        menuPage: '63eb5aae61459c1a0087f1a7',
        menuPageRef: 'premiumnormalPage',
        menuPageType: 'normalPage',
        menuUrlName: 'premium12',
        menuTitle: 'Premium',
        menuOrder: '2',
        menuIcon: 'Star',
      },
      {
        _id: '640824c1883eac56d425cda6',
        menuPage: '63eb5c6561459c1a0087f47c',
        menuPageRef: 'latestnormalPage',
        menuPageType: 'normalPage',
        menuUrlName: 'latestpage',
        menuTitle: 'Latest',
        menuOrder: '3',
        menuIcon: 'Star',
      },
      {
        _id: '640824c1883eac56d425cda7',
        menuPage: '63eb6c4761459c1a0087fee2',
        menuPageRef: 'indepthnormalPage',
        menuPageType: 'normalPage',
        menuUrlName: 'In Depth',
        menuTitle: 'Indepth',
        menuOrder: '4',
        menuIcon: 'Star',
      },
      {
        _id: '640824c1883eac56d425cda8',
        menuPage: '63eb78d961459c1a00880253',
        menuPageRef: 'trendingnormalPage',
        menuPageType: 'normalPage',
        menuUrlName: 'trendingpage',
        menuTitle: 'Trending',
        menuOrder: '5',
        menuIcon: 'Star',
      },
      {
        _id: '64097e52883eac56d425dc70',
        menuPage: '63ec7c1361459c1a00881135',
        menuPageRef: 'PodcastnormalPage',
        menuPageType: 'normalPage',
        menuUrlName: 'podcast',
        menuTitle: 'Podcast',
        menuOrder: '6',
        menuIcon: 'Profile',
      },
    ],
    externalUrl: null,
    internalUrl: null,
    menuIcon: 'Document',
    menuOrder: '2',
    application: AppService.appId,
    date: '2023-03-06T11:36:27.453Z',
    __v: 0,
  },
  {
    _id: '63a43de424e7dd49c0b0564b',
    menuTitle: 'Home',
    urlName: 'home',
    menuType: 'normalPage',
    menuPage: '611fcdc104968a42b82209c1',
    items: null,
    externalUrl: null,
    internalUrl: null,
    menuIcon: 'Home',
    menuOrder: '1',
    application: AppService.appId,
    date: '2022-12-22T11:22:12.569Z',
    __v: 0,
  },
  {
    _id: '6403a320883eac56d425c656',
    menuTitle: 'Live',
    urlName: 'live',
    menuType: 'livePage',
    menuPage: '612142b788e4090df0cc1ae5',
    items: null,
    externalUrl: null,
    internalUrl: null,
    menuIcon: 'Live',
    menuOrder: '7',
    application: AppService.appId,
    date: '2023-03-04T19:59:28.422Z',
    __v: 0,
  },
  {
    _id: '64097a93883eac56d425da24',
    menuTitle: 'Programs',
    urlName: 'ebuzz',
    menuType: 'multiple',
    menuPage: '64097995883eac56d425d98d',
    items: [
      {
        _id: '64097b18883eac56d425da37',
        menuPage: '64072186883eac56d425cbc0',
        menuPageRef: 'YathranormalPage',
        menuPageType: 'normalPage',
        menuUrlName: 'Yathranormal',
        menuTitle: 'Yathra',
        menuOrder: '1',
        menuIcon: 'Video',
      },
      {
        _id: '64097b18883eac56d425da38',
        menuPage: '64072186883eac56d425cbb5',
        menuPageRef: 'VakradrishtinormalPage',
        menuPageType: 'normalPage',
        menuUrlName: 'Vakradrishtinormal',
        menuTitle: 'Vakradrishti',
        menuOrder: '2',
        menuIcon: 'Video',
      },
      {
        _id: '64097b18883eac56d425da39',
        menuPage: '6407217b883eac56d425cbaa',
        menuPageRef: 'Master CraftnormalPage',
        menuPageType: 'normalPage',
        menuUrlName: 'Master Craftnormal',
        menuTitle: 'Master Craft',
        menuOrder: '3',
        menuIcon: 'Video',
      },
      {
        _id: '64097b18883eac56d425da3a',
        menuPage: '6407217b883eac56d425cb9f',
        menuPageRef: 'Dhim Tharikida ThomnormalPage',
        menuPageType: 'normalPage',
        menuUrlName: 'Dhim Tharikida Thomnormal',
        menuTitle: 'Dhim Tharikida Thom',
        menuOrder: '4',
        menuIcon: 'Video',
      },
      {
        _id: '64097b18883eac56d425da3b',
        menuPage: '64097995883eac56d425d977',
        menuPageRef: 'VaraphalamnormalPage',
        menuPageType: 'normalPage',
        menuUrlName: 'varaphalam',
        menuTitle: 'Varaphalam',
        menuOrder: '5',
        menuIcon: 'Video',
      },
      {
        _id: '64097b18883eac56d425da3c',
        menuPage: '64097995883eac56d425d96c',
        menuPageRef: 'xfilenormalPage',
        menuPageType: 'normalPage',
        menuUrlName: 'xfile',
        menuTitle: 'X-File',
        menuOrder: '6',
        menuIcon: 'Video',
      },
      {
        _id: '64097b18883eac56d425da3d',
        menuPage: '64097995883eac56d425d98d',
        menuPageRef: 'ebuzznormalPage',
        menuPageType: 'normalPage',
        menuUrlName: 'ebuzz',
        menuTitle: 'Ebuzz',
        menuOrder: '7',
        menuIcon: 'Video',
      },
    ],
    externalUrl: null,
    internalUrl: null,
    menuIcon: 'Photo',
    menuOrder: '5',
    application: AppService.appId,
    date: '2023-03-09T06:20:03.645Z',
    __v: 0,
  },
  {
    _id: '640985ca883eac56d425dfb1',
    menuTitle: 'More',
    urlName: 'Specials',
    menuType: 'multiple',
    menuPage: '64098486883eac56d425df12',
    items: [
      {
        _id: '64098634883eac56d425dfd4',
        menuPage: '64098486883eac56d425df12',
        menuPageRef: 'specialsnormalPage',
        menuPageType: 'normalPage',
        menuUrlName: 'Specials',
        menuTitle: 'Specials',
        menuOrder: '1',
        menuIcon: 'Dot Menu',
      },
      {
        _id: '64098634883eac56d425dfd5',
        menuPage: '64098461883eac56d425defc',
        menuPageRef: 'complaitnsexternalPage',
        menuPageType: 'externalPage',
        menuUrlName: 'complaints',
        menuTitle: 'Complaints',
        menuOrder: '2',
        menuIcon: 'Dot Menu',
      },
      {
        _id: '64098634883eac56d425dfd6',
        menuPage: '64098460883eac56d425def1',
        menuPageRef: 'contact usexternalPage',
        menuPageType: 'externalPage',
        menuUrlName: 'Contact us',
        menuTitle: 'Contact Us',
        menuOrder: '3',
        menuIcon: 'Dot Menu',
      },
      {
        _id: '64098634883eac56d425dfd7',
        menuPage: '64098460883eac56d425dee6',
        menuPageRef: 'aboutusexternalPage',
        menuPageType: 'externalPage',
        menuUrlName: 'About us',
        menuTitle: 'About Us',
        menuOrder: '4',
        menuIcon: 'Dot Menu',
      },
      {
        _id: '64098634883eac56d425dfd8',
        menuPage: '64098460883eac56d425dedb',
        menuPageRef: 'broadcast detailsexternalPage',
        menuPageType: 'externalPage',
        menuUrlName: 'broadcastdetails',
        menuTitle: 'Broadcast Details',
        menuOrder: '5',
        menuIcon: 'Dot Menu',
      },
      {
        _id: '64098634883eac56d425dfd9',
        menuPage: '64098460883eac56d425ded0',
        menuPageRef: 'privacypolicyexternalPage',
        menuPageType: 'externalPage',
        menuUrlName: 'privacypolicy',
        menuTitle: 'Privacy Policy',
        menuOrder: '6',
        menuIcon: 'Dot Menu',
      },
      {
        _id: '64098634883eac56d425dfda',
        menuPage: '64098460883eac56d425dec5',
        menuPageRef: 'complianceexternalPage',
        menuPageType: 'externalPage',
        menuUrlName: 'complaince',
        menuTitle: 'Compliance',
        menuOrder: '7',
        menuIcon: 'Dot Menu',
      },
    ],
    externalUrl: null,
    internalUrl: null,
    menuIcon: 'Dot Menu',
    menuOrder: '6',
    application: AppService.appId,
    date: '2023-03-09T07:07:54.936Z',
    __v: 0,
  },
  {
    _id: '6555bee9372a765e78580ff2',
    menuTitle: 'Forth Live',
    urlName: 'ebuzz',
    menuType: 'normalPage',
    menuPage: '64097995883eac56d425d98d',
    items: null,
    externalUrl: null,
    internalUrl: null,
    menuIcon: 'Home',
    menuOrder: '8',
    application: AppService.appId,
    date: '2023-11-16T07:04:09.151Z',
    __v: 0,
  },
];

// constants.js
export let colors = [];

export const setColors = (newColors) => {
  colors = newColors; // Update the exported variable
};

export const imageUrl = 'https://smottstorage.s3.us-east-2.amazonaws.com/';

export const appSettings = [
  {
    _id: '61d40954fe73466b040fb953',
    actionBarStyle: 'Dark',
    androidMinVersion: '',
    iOSMinVersion: '',
    androidTvMinVersion: '',
    appleTvMinVersion: '',
    fireTvMinVersion: '',
    rokuMinVersion: '',
    SamsungMinVersion: '',
    LGMinVersion: '',
    loadingSpinner: 'Ripple',
    landscapeOrientation: false,
    Casting: true,
    pip: true,
    subtitles: true,
    playinSmallWindow: true,
    application: AppService.appId,
    date: '2022-01-04T08:46:12.060Z',
    __v: 0,
    imageBorder: false,
  },
];

export const branding = [
  {
    _id: '611f78fcb4060657ac08399e',
    pageTitle: null,
    mainLogo: '6409841b883eac56d425dec4',
    footerLogo: '64058a8b883eac56d425c67d',
    iconLogo: '64058a8b883eac56d425c67d',
    footerDescription: null,
    application: AppService.appId,
    date: '2021-08-20T09:42:20.882Z',
    __v: 0,
    appIcon: '64058a8b883eac56d425c67d',
    chromeCastLogo: '613f811c8bc2ff1b0c681ef2',
    marketingLogo: '613f811c8bc2ff1b0c681ef2',
    mobileLogo: '64058a8b883eac56d425c67d',
    tvLogo: '6409841b883eac56d425dec4',
    chromeCastDarkLogo: '64058a8b883eac56d425c67d',
    footerDarkLogo: '6409841b883eac56d425dec4',
    mainDarkLogo: '64058a8b883eac56d425c67d',
    mobileDarkLogo: '64058a8b883eac56d425c67d',
    tvDarkLogo: '64058a8b883eac56d425c67d',
  },
];

export const imageKey = '7e15eac7b0660c84e4dfc073779b8eab.png';

export const AuthScreenConstsEn = {
  title: 'Please enter your application ID:',
  appId: 'App ID',
  submit: 'Submit',
}

export const AuthScreenConstsAr = {
  title: 'الرجاء إدخال معرف التطبيق الخاص بك:',
  appId: 'معرف التطبيق',
  submit: 'إرسال',
}
export const iconMap: Record<string, string> = {
  Home: "home",
  Music: "music",
  Star: "star",
  Document: "file",
  Padlock: "lock",
  Dollar: "currency-usd",
  Crown: "crown",
  Audio: "headphones",
  Profile: "account",
  "Bar Menu": "menu",
  "Dot Menu": "dots-vertical",
  Bell: "bell",
  Video: "video",
  Sort: "sort",
  Photo: "image",
  Eye: "eye",
  Search: "magnify",
  Movie: "movie",
  Setting: "cog",
  Globe: "earth",
  Folder: "folder",
  Paint: "brush",
  Cast: "cast",
  Refresh: "refresh",
  Play: "play",
  Stop: "stop",
  Pause: "pause",
  Trash: "trash-can",
  Close: "close",
  "Back Arrow": "arrow-left",
  "Forward Arrow": "arrow-right",
  "Up Arrow": "arrow-up",
  "Down Arrow": "arrow-down",
  Live: "broadcast",
};