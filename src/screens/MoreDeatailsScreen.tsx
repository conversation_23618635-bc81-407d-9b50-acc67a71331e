import * as React from "react";
import { useCallback } from "react";
import { useState } from "react";
import Banner from "../components/Banner";
import PortraitList from "../components/PortraitList";
import LandscapeList from "../components/LandscapeList";
import SquareList from "../components/SquareList";
import AdBanner from "../components/AdBanner";
import FullPortraitList from "../components/FullPortraitList";
import FullLandscapeList from "../components/FullLandscapeList";
import LandscapeListDes from "../components/LandscapeListDesc";
import NewsLandscape from "../components/NewsLandScape";
import FullLandscapeListImageView from "../components/FullLandscapeListImageView";
import ThreedLandscapeList from "../components/ThreeDLandscapeList";
import ThreedCarousal from "../components/ThreeDCarousel";
import BigPortraitList from "../components/BigPortraitList";
import MovieCarouselList from "../components/MovieCarousalList";
import NewArticleFullImage from "../components/NewArticleFullImage";
import Spinner from "react-native-loading-spinner-overlay/lib";

import {
  StyleSheet,
  View,
  SafeAreaView,
  FlatList,
  ActivityIndicator,
} from "react-native";
import TabListLandscape from "../components/TabListLandscape";
import FullLandscapeListImageViewdouble from "../components/FullLandscapeListImageViewDouble";
import TextGrid from "../components/TextGrid";
import HightlightTextGrid from "../components/HighLightTextGrid";
import { useFocusEffect } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../redux/store";
import { fetchData, incrementListCount } from "../redux/MainScreenSlice";

const MoreDetailsScreen = ({ route, props }) => {
  const { itemurlname, color, appsettinglist } = route.params;
  const dispatch = useDispatch<AppDispatch>();
  const { car, isLoading, listCount, totalListCount } = useSelector(
    (state: RootState) => state.mainScreen
  );
  // const [car, setCar] = useState([]);
  const compo = [];
  // const [isLoading, setIsLoading] = useState(false);
  // const [TotalListCount, setTotalListCount] = useState(0);
  // const [spinner, setspinner] = useState(true);
  // const timer = setTimeout(() => {
  //   setspinner(false);
  // }, 2600);

  useFocusEffect(
    useCallback(() => {
      if (itemurlname) {
        dispatch(fetchData({ urlname: itemurlname, listCount }));
      }
    }, [itemurlname, listCount, dispatch])
  );
  // useEffect(() => {
  //   car.length == 0 ? getdata() : null;
  // }, []);

  // useCallback(() => {
  //   car.length == 0 ? getdata() : null;
  // }, []);

  const LoadMoreItem = () => {
    dispatch(incrementListCount());
    dispatch(fetchData({ urlname: itemurlname, listCount }));
  };

  const renderLoader = () => {
    return isLoading ? (
      <View style={styles.loaderStyle}>
        <ActivityIndicator size="small" color="#aaa" />
      </View>
    ) : null;
  };

  function componentlist() {
    {
      car.map((item, key) => {

        console.log("more details, item.itemid.elementType=>   ", item.itemid.elementType);

        if (
          item.itemid.elementType == "adBanner" &&
          item.itemid.devices[2].itemName == "iOS" &&
          item.itemid.devices[2].itemVisibility == true
        ) {
          compo.push({
            compname: AdBanner,
            listtitle: item.itemid.displayName,
            seemoretext: "",
            url: "https://www.google.com",
            urltype: "IN",
            datas: item.itemid,
          });
        } else if (
          item.itemid.elementType == "portraitSlider" &&
          item.itemid.devices[2].itemName == "iOS" &&
          item.itemid.devices[2].itemVisibility == true
        ) {
          compo.push({
            compname: PortraitList,
            seemorecomp: "FullPortraitList",
            listtitle: item.itemid.displayName,
            seemoretext: item.itemid.viewAllText,
            seemoretextvis: item.itemid.viewAllVisibility,
            datas: item.itemid,
          });
        } else if (
          item.itemid.elementType == "landscapeSlider" &&
          item.itemid.devices[2].itemName == "iOS" &&
          item.itemid.devices[2].itemVisibility == true
        ) {
          compo.push({
            compname: LandscapeList,
            seemorecomp: "FullLandscapeList",
            listtitle: item.itemid.displayName,
            seemoretext: item.itemid.viewAllText,
            seemoretextvis: item.itemid.viewAllVisibility,
            datas: item.itemid,
          });
        } else if (
          item.itemid.elementType == "newsCarousel" &&
          item.itemid.devices[2].itemName == "iOS" &&
          item.itemid.devices[2].itemVisibility == true
        ) {
          compo.push({
            compname: Banner,
            listtitle: item.itemid.displayName,
            seemoretext: "",
            datas: item.itemid,
          });
        } else if (
          item.itemid.elementType == "movieCarousel" &&
          item.itemid.devices[2].itemName == "iOS" &&
          item.itemid.devices[2].itemVisibility == true
        ) {
          compo.push({
            compname: MovieCarouselList,
            listtitle: item.itemid.displayName,
            seemoretext: "",
            datas: item.itemid,
          });
        } else if (
          item.itemid.elementType == "squareSlider" &&
          item.itemid.devices[2].itemName == "iOS" &&
          item.itemid.devices[2].itemVisibility == true
        ) {
          compo.push({
            compname: SquareList,
            seemorecomp: "FullSquareList",
            listtitle: item.itemid.displayName,
            seemoretext: item.itemid.viewAllText,
            seemoretextvis: item.itemid.viewAllVisibility,
            datas: item.itemid,
          });
        } else if (
          item.itemid.elementType == "articleSlider" &&
          item.itemid.devices[2].itemName == "iOS" &&
          item.itemid.devices[2].itemVisibility == true
        ) {
          compo.push({
            compname: NewsLandscape,
            seemorecomp: "FullListPortrait",
            listtitle: item.itemid.displayName,
            seemoretext: item.itemid.viewAllText,
            seemoretextvis: item.itemid.viewAllVisibility,
            datas: item.itemid,
          });
        } else if (
          item.itemid.elementType == "newsList" &&
          item.itemid.devices[2].itemName == "iOS" &&
          item.itemid.devices[2].itemVisibility == true
        ) {
          compo.push({
            compname: NewArticleFullImage,
            seemorecomp: "LandscapeListDec",
            listtitle: item.itemid.displayName,
            seemoretext: item.itemid.viewAllText,
            seemoretextvis: item.itemid.viewAllVisibility,
            datas: item.itemid,
          });
        } else if (
          item.itemid.elementType == "bigportraitSlider" &&
          item.itemid.devices[2].itemName == "iOS" &&
          item.itemid.devices[2].itemVisibility == true
        ) {
          compo.push({
            compname: BigPortraitList,
            seemorecomp: "LandscapeListDec",
            listtitle: item.itemid.displayName,
            seemoretext: item.itemid.viewAllText,
            seemoretextvis: item.itemid.viewAllVisibility,
            datas: item.itemid,
          });
        } else if (
          item.itemid.elementType == "newsSlider" &&
          item.itemid.devices[2].itemName == "iOS" &&
          item.itemid.devices[2].itemVisibility == true
        ) {
          compo.push({
            compname: NewsLandscape,
            seemorecomp: "LandscapeListDec",
            listtitle: item.itemid.displayName,
            seemoretext: item.itemid.viewAllText,
            seemoretextvis: item.itemid.viewAllVisibility,
            datas: item.itemid,
          });
        } else if (
          item.itemid.elementType == "3dCarousel" &&
          item.itemid.devices[2].itemName == "iOS" &&
          item.itemid.devices[2].itemVisibility == true
        ) {
          compo.push({
            compname: ThreedCarousal,
            seemorecomp: "LandscapeListDec",
            listtitle: item.itemid.displayName,
            seemoretext: item.itemid.viewAllText,
            seemoretextvis: item.itemid.viewAllVisibility,
            datas: item.itemid,
          });
        } else if (
          item.itemid.elementType == "multiCarousel" &&
          item.itemid.devices[2].itemName == "iOS" &&
          item.itemid.devices[2].itemVisibility == true
        ) {
          compo.push({
            compname: ThreedLandscapeList,
            seemorecomp: "LandscapeListDec",
            listtitle: item.itemid.displayName,
            seemoretext: item.itemid.viewAllText,
            seemoretextvis: item.itemid.viewAllVisibility,
            datas: item.itemid,
          });
        } else if (
          item.itemid.elementType == "tabItem" &&
          item.itemid.devices[2].itemName == "iOS" &&
          item.itemid.devices[2].itemVisibility == true
        ) {
          compo.push({
            compname: TabListLandscape,
            seemorecomp: "LandscapeListDec",
            listtitle: item.itemid.displayName,
            seemoretext: item.itemid.viewAllText,
            seemoretextvis: item.itemid.viewAllVisibility,
            datas: item.itemid,
          });
        } else if (
          item.itemid.elementType == "grid" &&
          item.itemid.devices[2].itemName == "iOS" &&
          item.itemid.devices[2].itemVisibility == true
        ) {
          compo.push({
            compname: FullLandscapeListImageView,
            seemorecomp: "LandscapeListDec",
            listtitle: item.itemid.displayName,
            seemoretext: item.itemid.viewAllText,
            seemoretextvis: item.itemid.viewAllVisibility,
            datas: item.itemid,
          });
        } else if (
          item.itemid.elementType == "doubleGrid" &&
          item.itemid.devices[2].itemName == "iOS" &&
          item.itemid.devices[2].itemVisibility == true
        ) {
          compo.push({
            compname: FullLandscapeListImageViewdouble,
            seemorecomp: "LandscapeListDec",
            listtitle: item.itemid.displayName,
            seemoretext: item.itemid.viewAllText,
            seemoretextvis: item.itemid.viewAllVisibility,
            datas: item.itemid,
          });
        } else if (
          item.itemid.elementType == "textGrid" &&
          item.itemid.devices[2].itemName == "iOS" &&
          item.itemid.devices[2].itemVisibility == true
        ) {
          compo.push({
            compname: TextGrid,
            seemorecomp: "LandscapeListDec",
            listtitle: item.itemid.displayName,
            seemoretext: item.itemid.viewAllText,
            seemoretextvis: item.itemid.viewAllVisibility,
            datas: item.itemid,
          });
        } else if (
          item.itemid.elementType == "articleList" &&
          item.itemid.devices[2].itemName == "iOS" &&
          item.itemid.devices[2].itemVisibility == true
        ) {
          compo.push({
            compname: LandscapeListDes,
            seemorecomp: "LandscapeListDec",
            listtitle: item.itemid.displayName,
            seemoretext: item.itemid.viewAllText,
            seemoretextvis: item.itemid.viewAllVisibility,
            datas: item.itemid,
          });
        } else if (
          item.itemid.elementType == "highlightTextGrid" &&
          item.itemid.devices[2].itemName == "iOS" &&
          item.itemid.devices[2].itemVisibility == true
        ) {
          compo.push({
            compname: HightlightTextGrid,
            seemorecomp: "LandscapeListDec",
            listtitle: item.itemid.displayName,
            seemoretext: item.itemid.viewAllText,
            seemoretextvis: item.itemid.viewAllVisibility,
            datas: item.itemid,
          });
        } else if (
          item.itemid.elementType == "landscapeList" &&
          item.itemid.devices[2].itemName == "iOS" &&
          item.itemid.devices[2].itemVisibility == true
        ) {
          compo.push({
            compname: FullLandscapeList,
            seemorecomp: "LandscapeListDec",
            listtitle: item.itemid.displayName,
            seemoretext: item.itemid.viewAllText,
            seemoretextvis: item.itemid.viewAllVisibility,
            datas: item.itemid,
          });
        } else if (
          item.itemid.elementType == "portraitList" &&
          item.itemid.devices[2].itemName == "iOS" &&
          item.itemid.devices[2].itemVisibility == true
        ) {
          compo.push({
            compname: FullPortraitList,
            seemorecomp: "LandscapeListDec",
            listtitle: item.itemid.displayName,
            seemoretext: item.itemid.viewAllText,
            seemoretextvis: item.itemid.viewAllVisibility,
            datas: item.itemid,
          });
        }
      });
    }
  }

  return (
    <SafeAreaView
      style={{ flex: 1, backgroundColor: color.primaryBackgroundColor }}
    >
      {componentlist()}
      <Spinner
        visible={isLoading}
        textContent={"Loading..."}
        textStyle={{ color: color.primaryHighlightColor }}
        overlayColor={color.primaryBackgroundColor}
        color={color.primaryHighlightColor}
      />
      <FlatList
        showsHorizontalScrollIndicator={false}
        data={compo}
        renderItem={({ item }) => (
          <item.compname
            color={color}
            seemorecomp={item.seemorecomp}
            listtitle={item.listtitle}
            seemoretext={item.seemoretext}
            seemoretextvis={item.seemoretextvis}
            url={item.url}
            urltype={item.urltype}
            datas={item.datas}
            appsettings={appsettinglist}
          />
        )}
        ListFooterComponent={renderLoader}
        onEndReached={car.length != totalListCount ? LoadMoreItem : null}
        onEndReachedThreshold={0}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  loaderStyle: {
    marginVertical: 16,
    alignItems: "center",
  },
});
export default MoreDetailsScreen;
