import * as React from 'react';
import { Text, Dimensions, View, StyleSheet, SafeAreaView, ScrollView } from 'react-native';
import { useState } from 'react';
import axios from 'axios';
import { useNavigation } from '@react-navigation/native';
import { TouchableOpacity } from 'react-native';
import { FlatList } from 'react-native';
import { ActivityIndicator } from 'react-native';
import { Image } from 'react-native-elements';
import { Fragment } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';

const { width } = Dimensions.get('window');

const Favorites = () => {
  const color = useSelector((state: RootState) => state.homeScreen.colors);
  const isRTL = useSelector((state: RootState) => state.homeScreen.isRTL);
  const navigation = useNavigation();
  const [JwData, setJwData] = useState([]);
  const [ListID, setListID] = useState();
  const styles = StyleSheet.create({
    row: {
      flex: 1,
      justifyContent: 'space-around',
    },
    inputWrap: {
      flex: 1,
      borderColor: color.primaryTextColor,
    },
    text1: {
      color: color.primaryTextColor,
      fontSize: width / 25,
      fontWeight: 'bold',
      left: width / 40,
      textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
    },
    text2: {
      color: color.primaryTextColor,
      fontSize: width / 30,
      fontWeight: 'bold',
      right: width / 40,
      textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
    },
  });

  const getdata = async () => {
    try {
      const result = await axios.get(
        'https://cdn.jwplayer.com/v2/playlists/Pzc82jPy?search=' + ListID
      );
      console.log('sdcv', result.data.playlist);
      setJwData(result.data.playlist);
    } catch (e) {
      console.log(e);
      throw e;
    }
  };

  // React.useEffect(() => {
  //   getdata();
  // }, [getdata]);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <Fragment>
        <ScrollView showsVerticalScrollIndicator={false} nestedScrollEnabled={true}>
          <View style={{ marginBottom: width / 25, marginTop: width / 25 }}>
            <View style={styles.row}></View>
            <View style={styles.inputWrap}>
              <Text style={styles.text1}>Favourites</Text>
            </View>
            <View style={{ marginBottom: width / 45 }}>
              <FlatList
                style={{ marginTop: width / 50 }}
                numColumns={2} // set number of columns
                // columnWrapperStyle={styles.row}
                scrollEnabled={false}
                showsHorizontalScrollIndicator={false}
                data={JwData}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    onPress={() => {
                      navigation.navigate('Details', {
                        item: item,
                        color: color,
                      });
                    }}>
                    <Image
                      PlaceholderContent={<ActivityIndicator />}
                      source={{ uri: item.image }}
                      style={{
                        width: width / 2.1,
                        height: width / 3.6,
                        borderWidth: 0,
                        borderColor: '#d35647',
                        borderRadius: width / 50,
                        resizeMode: 'contain',
                        margin: width / 85,
                        // marginTop:5
                      }}
                    />
                  </TouchableOpacity>
                )}
              />
            </View>
          </View>
        </ScrollView>
      </Fragment>
    </SafeAreaView>
  );
};

export default Favorites;
