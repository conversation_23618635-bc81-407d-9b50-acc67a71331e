import * as React from "react";

import { View, Text, SafeAreaView, Dimensions } from "react-native";
import { useSelector } from "react-redux";
import { RootState } from "../redux/store";

const { width, height } = Dimensions.get("window");

const PageNotAvailable = () => {
  const color = useSelector((state: RootState) => state.homeScreen.colors);

  return (
    <SafeAreaView
      style={{ flex: 1, backgroundColor: color.primaryBackgroundColor }}
    >
      <View
        style={{
          alignContent: "center",
          alignItems: "center",
          justifyContent: "center",
          alignSelf: "center",
          height: height - width / 2,
        }}
      >
        <Text
          style={{
            textAlign: "center",
            fontWeight: "bold",
            color: color.primaryTextColor,
            fontSize: width / 20,
          }}
        >
          Page Not Available
        </Text>
      </View>
    </SafeAreaView>
  );
};

export default PageNotAvailable;
