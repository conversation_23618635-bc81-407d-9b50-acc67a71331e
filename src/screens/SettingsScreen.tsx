import * as React from "react";
import { View, SafeAreaView, Dimensions, Linking, Alert } from "react-native";
import { ListItem } from "react-native-elements";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { useNavigation } from "@react-navigation/native";
import { useSelector } from "react-redux";
import { RootState } from "../redux/store";

const { width } = Dimensions.get("window");

type MenuItem = {
  menuIcon: string;
  menuType: string;
  externalUrl?: string;
  menuTitle: string;
  urlName?: string;
  menuOrder: string;
};

type Props = {
  nav: MenuItem[];
};

const SettingsScreen: React.FC<Props> = (props) => {
  console.log("SettingsScreen => ", props);
  const navigation = useNavigation();
  const colors = useSelector((state: RootState) => state.homeScreen.colors);
  const isRTL = useSelector((state: RootState) => state.homeScreen.isRTL);
  const menuicon: string[] = [];

  function menuiconlist() {
    // props.nav.sort((a, b) => parseInt(a.menuOrder) - parseInt(b.menuOrder));
    props.nav.forEach((item) => {
      console.log("menu item->>>>", item);

      switch (item.menuIcon) {
        case "Home":
          if (item.menuType === 'livePage') {
            menuicon.push("youtube-tv");
          } else {
            menuicon.push("home");
          }
          break;
        case "Music":
          menuicon.push("music");
          break;
        case "Star":
          menuicon.push("star");
          break;
        case "Document":
          menuicon.push("file");
          break;
        case "Padlock":
          menuicon.push("lock");
          break;
        case "Doller":
          menuicon.push("doller");
          break;
        case "Crown":
          menuicon.push("crown");
          break;
        case "Audio":
          menuicon.push("audio");
          break;
        case "Profle":
          menuicon.push("profle");
          break;
        case "Bar Menu":
          menuicon.push("bar");
          break;
        case "Dot Menu":
          menuicon.push("dots-vertical");
          break;
        case "Bell":
          menuicon.push("bell");
          break;
        case "Video":
          menuicon.push("video");
          break;
        case "Sort":
          menuicon.push("sort");
          break;
        case "Photo":
          menuicon.push("image-area");
          break;
        case "Eye":
          menuicon.push("eye");
          break;
        case "Search":
          menuicon.push("search");
          break;
        case "Movie":
          menuicon.push("movie");
          break;
        case "Setting":
          menuicon.push("setting");
          break;
        case "Globe":
          menuicon.push("earth");
          break;
        case "Folder":
          menuicon.push("folder");
          break;
        case "Paint":
          menuicon.push("paint");
          break;
        case "Cast":
          menuicon.push("cast");
          break;
        case "Refresh":
          menuicon.push("refresh");
          break;
        case "Play":
          menuicon.push("play");
          break;
        case "Stop":
          menuicon.push("stop");
          break;
        case "Pause":
          menuicon.push("pause");
          break;
        case "Trash":
          menuicon.push("trash");
          break;
        case "Close":
          menuicon.push("close");
          break;
        case "Back Arrow":
          menuicon.push("back");
          break;
        case "Forward Arrow":
          menuicon.push("forward");
          break;
        case "Up Arrow":
          menuicon.push("up");
          break;
        case "Down Arrow":
          menuicon.push("down");
          break;
        case "Live":
          menuicon.push("youtube-tv");
          break;
        default:
          menuicon.push("home");
          break;
      }
    });
  }

  const showAlert = (title: string, content: string) => {
    Alert.alert(
      title,
      content,
      [{ text: 'OK', onPress: () => console.log('OK Pressed') }]
    );
  };

  function navigationlist(
    pageType: string,
    itemnavigation: string | undefined,
    itemdata: MenuItem,
    urlname: string | undefined
  ) {
    console.log(
      "navigationlist =>",
      pageType,
      itemnavigation,
      itemdata,
      urlname
    );
    if (pageType === "externalPage") {
      if (itemnavigation) {
        return Linking.openURL(itemnavigation);
      } else {
        console.log('unable to navigate');
        showAlert('Error', 'Failed to open link');
      }
    } else if (pageType === "articlePage") {
      console.log("itemdata---------------------", itemdata);
      return navigation.push("ArticleDetails", {
        item: itemdata,
        color: colors,
      });
    } else if (pageType === "seasonPage") {
      return navigation.push("Season", {
        item: itemdata,
        color: colors,
      });
    } else if (pageType === "videoPage") {
      return navigation.push("Details", {
        item: itemdata,
        color: colors,
      });
    } else if (pageType === "normalPage") {
      return navigation.push("MoreDetail", {
        itemurlname: urlname,
        color: colors,
      });
    } else if (pageType === "livePage") {
      console.log(itemdata);

      return navigation.push('LiveStreamScreen')
    } else {
      showAlert('Error', 'No data found');

    }
  }

  menuiconlist();

  return (
    <SafeAreaView
      style={{ flex: 1, backgroundColor: colors.primaryBackgroundColor }}
    >
      <View style={{ direction: isRTL ? 'rtl' : 'ltr' }}>
        {props.nav.map((item, i) =>
          <ListItem
            onPress={() => {
              navigationlist(
                item.menuType,
                item.externalUrl,
                item,
                item.urlName
              );
            }}
            key={i}
            bottomDivider
            containerStyle={{ backgroundColor: colors.secondarycolor }}
          >
            <MaterialCommunityIcons
              name={menuicon[i]}
              color={colors.primaryTextColor}
              size={width / 25}
            />
            <ListItem.Content>
              <ListItem.Title
                style={{
                  color: colors.primaryTextColor,
                  fontSize: width / 25,
                }}
              >
                {item.menuTitle}
              </ListItem.Title>
            </ListItem.Content>
            <ListItem.Chevron style={{ transform: [{ scaleX: isRTL ? -1 : 1 }] }} />
          </ListItem>

        )}
      </View>
    </SafeAreaView >
  );
};

export default SettingsScreen;
