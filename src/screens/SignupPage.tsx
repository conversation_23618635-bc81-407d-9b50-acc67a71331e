import * as React from "react";
import {
  Text,
  Image,
  Dimensions,
  View,
  SafeAreaView,
  ScrollView,
  Platform,
  TextInput,
  TouchableOpacity,
} from "react-native";
import { useEffect, useState, useRef } from "react";
import { useNavigation } from "@react-navigation/native";
import DateTimePicker from "@react-native-community/datetimepicker";
import PhoneInput from "react-native-phone-number-input";
import InPlayer from "@inplayer-org/inplayer.js";
import { useSelector } from "react-redux";
import { RootState } from "../redux/store";
import AsyncStorage from "@react-native-async-storage/async-storage";

const { width, height } = Dimensions.get("window");

const SignUpPage = () => {
  const navigation = useNavigation();
  const image = useSelector((state: RootState) => state.homeScreen.imageKey);
  const color = useSelector((state: RootState) => state.homeScreen.colors);
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);

  const [phoneNumber, setPhoneNumber] = useState("");
  const phoneInput = useRef(null);
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [repassword, setRepassword] = useState("");
  const [birth, setBirth] = useState("");
  const [date, setDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [focusedField, setFocusedField] = useState("name");

  const nameInputRef = useRef<TextInput>(null);

  useEffect(() => {
    nameInputRef.current?.focus(); // Auto-focus the first input field
  }, []);

  const signUp = async () => {
    try {
      const response = await InPlayer.Account.signUp({
        fullName: name,
        email,
        password,
        passwordConfirmation: repassword,
        date_of_birth: birth,
        grantType: "password",
        clientId: "556f2de2-6d52-42c3-a76d-9a91576b0548",
        type: "consumer",
        referrer: "http://localhost:3001",
        metadata: {
          country: "India",
          phoneNumber,
          phone_verifed: false,
          watchHistory: JSON.stringify([]),
          favourites: JSON.stringify([]),
          gdpr: false,
          cookies: false,
          settings: JSON.stringify({}),
          profiles: JSON.stringify({}),
        },
      });
      console.log("Signup Success:", response);
      navigation.navigate("Home");
    } catch (error) {
      console.error(
        "Signup Error:",
        error.response?.data?.message || error.message
      );
      alert(error.response?.data?.message || "Signup failed");
    }
  };

  return (
    <SafeAreaView
      style={{ flex: 1, backgroundColor: color.primaryBackgroundColor }}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        nestedScrollEnabled={true}
      >
        <View
          style={{
            alignItems: "center",
            marginTop: height / 30,
            width: width / 1.1,
          }}
        >
          <Image
            style={{
              width: width / 5,
              height: width / 17,
              marginBottom: width / 15,
            }}
            source={{ uri: imageUrl + image }}
          />

          {/* Name Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.label(color)}>Name</Text>
            <TextInput
              ref={nameInputRef}
              style={styles.inputField(color, focusedField === "name")}
              placeholder=""
              onFocus={() => setFocusedField("name")}
              onBlur={() => setFocusedField("")}
              onChangeText={setName}
            />
          </View>

          {/* Email Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.label(color)}>Email</Text>
            <TextInput
              style={styles.inputField(color, focusedField === "email")}
              placeholder=""
              keyboardType="email-address"
              onFocus={() => setFocusedField("email")}
              onBlur={() => setFocusedField("")}
              onChangeText={setEmail}
            />
          </View>

          {/* Phone Input */}
          <View style={styles.inputContainer}>
            <PhoneInput
              ref={phoneInput}
              value={phoneNumber} // <-- Use value instead of defaultValue
              defaultCode="IN"
              layout="first"
              withShadow
              containerStyle={styles.phoneContainer}
              textContainerStyle={styles.phoneTextContainer(color)}
              textInputStyle={{ color: color.primaryTextColor, height: 60 }} // Ensure text is visible
              codeTextStyle={{ color: color.primaryTextColor, height: 20 }}
              onChangeFormattedText={(text) => setPhoneNumber(text)}
            />
          </View>

          {/* DOB Picker */}
          <View style={styles.inputContainer}>
            <Text style={styles.label(color)}>DOB</Text>
            <TouchableOpacity onPress={() => setShowDatePicker(true)}>
              <Text
                style={[
                  styles.inputField(color, false),
                  { paddingVertical: 12 },
                ]}
              >
                {birth || "Select Date"}
              </Text>
            </TouchableOpacity>
            {showDatePicker && (
              <DateTimePicker
                value={date}
                mode="date"
                display={Platform.OS === "ios" ? "spinner" : "default"}
                onChange={(event, selectedDate) => {
                  setShowDatePicker(false);
                  if (selectedDate) {
                    setDate(selectedDate);
                    setBirth(selectedDate.toISOString().split("T")[0]);
                  }
                }}
              />
            )}
          </View>

          {/* Password Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.label(color)}>Password</Text>
            <TextInput
              style={styles.inputField(color, focusedField === "password")}
              placeholder=""
              secureTextEntry
              onFocus={() => setFocusedField("password")}
              onBlur={() => setFocusedField("")}
              onChangeText={setPassword}
            />
          </View>

          {/* Repeat Password Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.label(color)}>Repeat Password</Text>
            <TextInput
              style={styles.inputField(color, focusedField === "repassword")}
              placeholder=""
              secureTextEntry
              onFocus={() => setFocusedField("repassword")}
              onBlur={() => setFocusedField("")}
              onChangeText={setRepassword}
            />
          </View>

          {/* Signup Button */}
          <TouchableOpacity
            onPress={signUp}
            style={{ marginBottom: width / 15 }}
          >
            <Text
              style={{
                fontSize: width / 20,
                color: color.primaryHighlightColor,
                fontWeight: "bold",
              }}
            >
              Create Account
            </Text>
          </TouchableOpacity>

          {/* Login Navigation */}
          <View style={{ flexDirection: "row", marginBottom: width / 15 }}>
            <Text
              style={{ fontSize: width / 35, color: color.primaryTextColor }}
            >
              Already have an account?
            </Text>
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <Text style={{ fontSize: width / 35, color: "blue" }}>
                {" "}
                Log in
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = {
  inputContainer: { marginBottom: width / 15, width: width / 1.5 },
  label: (color) => ({ fontSize: width / 35, color: color.primaryTextColor }),
  inputField: (color, isFocused) => ({
    height: 50,
    fontSize: 16,
    borderBottomWidth: 2,
    borderBottomColor: isFocused
      ? color.primaryHighlightColor
      : color.primaryTextColor,
    color: color.primaryTextColor,
    paddingVertical: Platform.OS === "ios" ? 10 : 0,
  }),
  phoneContainer: {
    height: 50,
    backgroundColor: "transparent",
    width: width / 1.4,
  },
  phoneTextContainer: (color) => ({
    backgroundColor: "transparent", // Ensure transparency
    borderBottomColor: color.primaryTextColor,
    borderBottomWidth: 2,
    borderRadius: 0, // Avoid unwanted borders
  }),
};

export default SignUpPage;
