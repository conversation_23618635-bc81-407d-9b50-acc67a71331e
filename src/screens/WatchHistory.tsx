import * as React from "react";
import {
  Text,
  Dimensions,
  View,
  StyleSheet,
  SafeAreaView,
  ScrollView,
} from "react-native";
import { useState } from "react";
import { useNavigation } from "@react-navigation/native";
import { TouchableOpacity } from "react-native";
import { FlatList } from "react-native";
import { ActivityIndicator } from "react-native";
import { Image } from "react-native-elements";
import { Fragment } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../redux/store";

const { width } = Dimensions.get("window");

const WatchHistory = () => {
  const color = useSelector((state: RootState) => state.homeScreen.colors);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const videoInfo = useSelector((state: RootState) => state.homeScreen.videoCloud);
  const navigation = useNavigation();
  const [JwData, setJwData] = useState([]);
  const [ListID, setListID] = useState();
  const [isYoutubeVideo, setIsYoutubeVideo] = useState(false);
  const [youtubeData, setYoutubeData] = useState<unknown[]>([]);


  // useEffect(() => {
  //   initVideoInfo();
  // }, []);

  // const initVideoInfo = async () => {
  //   try {
  //     if (!props.datas.playlistId) {
  //       return;
  //     }

  //     if (videoInfo[0].cloudName === 'YouTube') {
  //       const videoData = await VideoPlayerService.fetchYoutubePlaylist(props.datas.playlistId);
  //       setYoutubeData(videoData);
  //       setIsYoutubeVideo(true);
  //     } else {
  //       const videoData = await VideoPlayerService.fetchJwplayerPlaylist(props.datas.playlistId);
  //       setJwData(videoData);
  //     }
  //   } catch (error) {
  //     console.error('Error fetching playlist [WatchHistory]:', error);
  //   }
  // }

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <Fragment>
        <ScrollView
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled={true}
        >
          <View style={{ marginBottom: width / 25, marginTop: width / 25 }}>
            <View style={styles(color, isRTL).row}></View>
            <View style={styles(color, isRTL).inputWrap}>
              <Text style={styles(color, isRTL).text1}>Watch History</Text>
            </View>
            <View style={{ marginBottom: width / 45 }}>
              <FlatList
                style={{ marginTop: width / 50 }}
                numColumns={2} // set number of columns
                // columnWrapperStyle={styles(color, isRTL).row}
                scrollEnabled={false}
                showsHorizontalScrollIndicator={false}
                data={JwData}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    onPress={() => {
                      if (isYoutubeVideo) {
                        return navigation.push('YouTubeDetailsScreen', { item: item, color: props.color, });
                      } else {
                        return navigation.push('Details', { item: item, color: props.color, });
                      }
                    }}
                  >
                    <Image
                      PlaceholderContent={<ActivityIndicator />}
                      source={{ uri: item.image }}
                      style={{
                        width: width / 2.1,
                        height: width / 3.6,
                        borderWidth: 0,
                        borderColor: "#d35647",
                        borderRadius: width / 50,
                        // resizeMode:'contain',
                        margin: width / 85,
                        // marginTop:5
                      }}
                    />
                  </TouchableOpacity>
                )}
              />
            </View>
          </View>
        </ScrollView>
      </Fragment>
    </SafeAreaView>
  );
};

export default WatchHistory;

const styles = (color, isRTL) => StyleSheet.create({
  row: {
    flex: 1,
    justifyContent: "space-around",
  },
  inputWrap: {
    flex: 1,
    borderColor: color.primaryTextColor,
  },
  text1: {
    color: color.primaryTextColor,
    fontSize: width / 25,
    fontWeight: "bold",
    left: width / 40,
    textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',

  },
  text2: {
    color: color.primaryTextColor,
    fontSize: width / 30,
    fontWeight: "bold",
    textAlign: "right",
    right: width / 40,
    textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
  },
});