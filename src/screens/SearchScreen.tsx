import * as React from 'react';
import { Text, Dimensions, View, StyleSheet, SafeAreaView, ScrollView } from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useEffect, useState } from 'react';
import axios from 'axios';
import { useNavigation } from '@react-navigation/native';
import { TouchableOpacity } from 'react-native';
import { FlatList } from 'react-native';
import { ActivityIndicator } from 'react-native';
import { Image } from 'react-native-elements';
import { Fragment } from 'react';
import { TextInput } from 'react-native';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { VideoPlayerService } from '../services/VideoPlayerService';
import constant from '../config/constant';

const { width, height } = Dimensions.get('window');

const SearchScreen = () => {
  const color = useSelector((state: RootState) => state.homeScreen.colors);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const appsettinglist = useSelector((state: RootState) => state.homeScreen.appSettings);
  const videoInfo = useSelector((state: RootState) => state.homeScreen.videoCloud);
  const navigation = useNavigation();
  const [jwData, setJwData] = useState([]);
  const [ListID, setListID] = useState();
  const [spinner, setspinner] = useState(false);
  const [youtubeData, setYoutubeData] = useState<unknown[]>([]);
  const [isYoutubeVideo, setIsYoutubeVideo] = useState(false);
  const FullData = [];


  useEffect(() => {
    initVideoInfo();
  }, []);

  const initVideoInfo = async () => {
    try {
      if (!props.datas.playlistId) {
        return;
      }

      if (videoInfo[0].cloudName === 'YouTube') {
        const videoData = await VideoPlayerService.fetchYoutubePlaylist(props.datas.playlistId);
        setYoutubeData(videoData);
        setIsYoutubeVideo(true);
      } else {
        const videoData = await VideoPlayerService.fetchJwplayerPlaylist(props.datas.playlistId);
        setJwData(videoData);
      }
    } catch (error) {
      console.error('Error fetching playlist [ThreeDCarousal]:', error);
    }
  }


  function setPlayListData() {

    if (jwData.playlist != null) {
      jwData.playlist.forEach((item, key) => {
        FullData.push({
          imageUrl: item.image,
          data: item.title,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          displayname: item.title,
          introduction: item.description,
        });
      });

    }
    if (youtubeData != null) {
      youtubeData.forEach((item, key) => {
        FullData.push({
          imageUrl: item.thumbnail,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          tag: item.etag,
          mainHeader: item.title,
        });
      });
    }
  }

  const BackIcon = () => {
    return (
      <TouchableOpacity
        onPress={() => navigation.goBack()}
        style={{
          height: width / 15,
          width: width / 10,

        }}>
        <View>
          <MaterialCommunityIcons
            name="arrow-left-thin"
            color={color.primaryTextColor}
            size={width / 11}
            style={{
              transform: [{ scaleX: isRTL ? -1 : 1 }], // Flip the icon in RTL mode
            }}
          />
        </View>
      </TouchableOpacity>
    );
  }

  return (
    setPlayListData(),
    (
      <SafeAreaView style={{ flex: 1 }}>
        <Fragment>
          <View style={{ flexDirection: 'row', marginTop: width / 40, marginLeft: isRTL ? 16 : 5, marginRight: isRTL ? 5 : 16 }}>
            {!isRTL && BackIcon()}
            <View
              style={{
                flex: 1,
                padding: width / 40,
                borderColor: color.primaryTextColor,
                borderWidth: 1,
                borderRadius: width / 10,
              }}>
              <TextInput
                style={{
                  height: width / 30,
                  backgroundColor: 'transparent',
                  fontSize: width / 35,
                  color: color.primaryTextColor,
                  textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
                }}
                placeholder=" Search"
                onChangeText={(text) => {
                  setListID(text);
                  initVideoInfo();
                }}
              />
            </View>
            {isRTL && BackIcon()}
          </View>
          <ScrollView showsVerticalScrollIndicator={false} nestedScrollEnabled={true}>
            <View style={{ marginBottom: width / 25, marginTop: width / 25 }}>
              <View style={styles(color, isRTL).row}></View>
              {FullData.length === 0 ? (
                <View
                  style={{
                    alignContent: 'center',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: height - width / 1.5,
                  }}>
                  <Text style={styles(color, isRTL).text1}>No Results Found</Text>
                </View>
              ) : (
                <View style={styles(color, isRTL).inputWrap}>
                  <Text style={styles(color, isRTL).text1}>Search Results</Text>
                </View>
              )}
              <View style={{ marginBottom: width / 45 }}>
                <FlatList
                  style={{ marginTop: width / 50 }}
                  numColumns={2} // set number of columns
                  // columnWrapperStyle={styles(color,isRTL).row}
                  scrollEnabled={false}
                  showsHorizontalScrollIndicator={false}
                  data={FullData}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      onPress={() => {
                        if (isYoutubeVideo) {
                          return navigation.push('YouTubeDetailsScreen', { item: item, color: props.color, });
                        } else {
                          return navigation.push('Details', { item: item, color: props.color, });
                        }
                      }}>
                      <Image
                        PlaceholderContent={<ActivityIndicator />}
                        source={{ uri: item.image }}
                        style={{
                          width: width / 2.1,
                          height: width / 3.6,
                          borderWidth: 0,
                          borderColor: '#d35647',
                          borderRadius:
                            appsettinglist !== undefined
                              ? appsettinglist.imageBorder === false
                                ? width / 50
                                : 0
                              : null,
                          // resizeMode:'contain',
                          margin: width / 85,
                          // marginTop:5
                        }}
                      />
                    </TouchableOpacity>
                  )}
                />
              </View>
            </View>
          </ScrollView>
        </Fragment>
      </SafeAreaView>
    )
  );
};

export default SearchScreen;


const styles = (color, isRTL: boolean) => StyleSheet.create({
  row: {
    flex: 1,
    justifyContent: 'space-around',
  },
  inputWrap: {
    flex: 1,
    borderColor: color.primaryTextColor,
  },
  text1: {
    color: color.primaryTextColor,
    fontSize: width / 25,
    fontWeight: 'bold',
    marginHorizontal: width / 40,
    textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
  },
  text2: {
    color: color.primaryTextColor,
    fontSize: width / 30,
    fontWeight: 'bold',
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
    marginHorizontal: width / 40,
  },
});