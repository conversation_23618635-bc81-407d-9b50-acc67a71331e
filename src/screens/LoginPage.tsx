import * as React from "react";
import {
  Text,
  Image,
  Dimensions,
  View,
  SafeAreaView,
  ScrollView,
} from "react-native";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { useEffect, useState } from "react";
import { useNavigation } from "@react-navigation/native";
import { TouchableOpacity } from "react-native";
import { TextInput } from "react-native";
import { AsyncStorage } from "react-native";
import { useSelector } from "react-redux";
import { RootState } from "../redux/store";
import InPlayer from "@inplayer-org/inplayer.js";

const { width, height } = Dimensions.get("window");

const LoginPage = () => {
  const navigation = useNavigation();
  const mobilelogokey = useSelector(
    (state: RootState) => state.homeScreen.imageKey
  );
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);
  const color = useSelector((state: RootState) => state.homeScreen.colors);
  const [imagekey, setimagekey] = useState([]);
  const [brand, setBranding] = useState([]);
  const [inplayertoken, setinplayertoken] = useState([]);
  const [email, setemail] = useState("");
  const [password, setpassword] = useState("");
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);

  useEffect(() => {
    InPlayer.tokenStorage.overrides = {
      setItem: function (key, value) {
        // use something like https://reactnative.dev/docs/asyncstorage
        // console.log('fggfggfgfgf', value);
        try {
          AsyncStorage.setItem(key, value);
        } catch (error) {
          // Error saving data
        }
        // setinplayertoken(value);
        console.log("dfdfdfdfddfddfdf", value);

        // InPlayer.Account.setToken(value);
      },
      getItem: function (key) {
        try {
          AsyncStorage.getItem(key);
        } catch (error) {
          // Error saving data
        }
      },
      removeItem: function (key) {
        try {
          AsyncStorage.removeItem(key);
        } catch (error) {
          // Error saving data
        }
        // use something like https://reactnative.dev/docs/asyncstorage
      },
    };
    // const videoDetail = async () => {
    //   const token = await getToken();
    //   try {
    //     const result = await axios.get(
    //       'http://10.10.12.237:6050/api/view/60fd88d4a12aadad2037ac50/getAppSetup',
    //       {headers: {Authorization: token}},
    //     );
    //     console.log(result.data[0].color[0].primaryBackgroundColor);
    //     console.log(result.data[0].menu[0].menuTitle);
    //     setBranding(result.data[0].branding[0]);
    //     console.log(result.data[0].color[0]);
    //   } catch (e) {
    //     console.log(e);
    //     throw e;
    //   }
    // };
    // const imagekeyid = async () => {
    //   const token = await getToken();
    //   try {
    //     const result = await axios.get(
    //       'http://10.10.12.237:6050/api/aws/' + brand.mobileLogo + '/getImage',
    //       {headers: {Authorization: token}},
    //     );
    //     setimagekey(result.data.images.imagekey);
    //   } catch (e) {
    //     console.log(e);
    //     throw e;
    //   }
    // };
    // imagekeyid();
    // videoDetail();
  }, [brand.mobileLogo]);

  // async function userdetails() {
  //   const value = await AsyncStorage.getItem('inplayer_token');
  //   console.log('dhddhhddhdhdhdhdhhd', JSON.parse(value));
  //   setinplayertoken(JSON.parse(value));
  //   console.log('dhddhhddhdhdhdhdhhd', inplayertoken.token);
  //   try {
  //     const result = await axios.get(
  //       'https://staging-v2.inplayer.com/accounts',
  //       {headers: {Authorization: inplayertoken.token}},
  //     );
  //     console.log(result.data);
  //   } catch (e) {
  //     console.log(e);
  //     throw e;
  //   }
  // }
  async function login() {
    InPlayer.Account.signIn({
      email: email,
      password: password,
      clientId: "556f2de2-6d52-42c3-a76d-9a91576b0548",
    })
      .then((data) => {
        console.log("gfgdgfhwdfgdhgfgdhfgd", data);
        navigation.navigate("Home", {});
      })
      .catch((error) => {
        console.log("gfgdgfhwdfgdhgfgdhfgd", error);
        console.log("gfgdgfhwdfgdhgfgdhfgd", error.data);
        console.log("gfgdgfhwdfgdhgfgdhfgd", error.response);
        alert(JSON.stringify(error.response.data.message));
      });
  }

  // useFocusEffect(() => {}, []);

  const BackIcon = () => {
    return (
      <View style={{
        alignItems: isRTL ? 'flex-end' : 'flex-start',
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            height: width / 15,
            width: width / 10,
            marginHorizontal: width / 40,

          }}
        >
          <View>
            <MaterialCommunityIcons
              name="arrow-left-thin"
              color={color.primaryTextColor}
              size={width / 11}
              style={{
                transform: [{ scaleX: isRTL ? -1 : 1 }], // Flip the icon in RTL mode
              }}
            />
          </View>
        </TouchableOpacity>
      </View >
    );
  }

  return (
    <SafeAreaView
      style={{ flex: 1, backgroundColor: color.primaryBackgroundColor }}
    >
      <View>
        <BackIcon />
        <ScrollView
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled={true}
          style={{ height: height }}
        >
          <View
            style={{
              alignContent: "center",
              alignItems: "center",
              alignSelf: "center",
              marginTop: height / 5,
              width: width / 1.1,
            }}
          >
            <Image
              style={{
                width: width / 5,
                height: width / 17,
                marginBottom: width / 15,
              }}
              source={{
                uri: imageUrl + mobilelogokey,
              }}
            />
            <View style={{ marginBottom: width / 15 }}>
              <Text
                style={{
                  fontSize: width / 35, color: color.primaryTextColor,
                  textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
                }}
              >
                Email
              </Text>
              <TextInput
                style={{
                  height: width / 20,
                  backgroundColor: "transparent",
                  fontSize: width / 30,
                  width: width / 2,
                  borderColor: "transparent",
                  borderBottomColor: color.primaryTextColor,
                  borderWidth: 1,
                  color: color.primaryTextColor,
                }}
                placeholder=""
                textContentType="emailAddress"
                onChangeText={(text) => setemail(text)}
              />
            </View>
            <View style={{ marginBottom: width / 10 }}>
              <Text
                style={{
                  fontSize: width / 35, color: color.primaryTextColor,
                  textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
                }}
              >
                Password
              </Text>
              <TextInput
                style={{
                  height: width / 20,
                  backgroundColor: "transparent",
                  fontSize: width / 30,
                  width: width / 2,
                  borderColor: "transparent",
                  borderBottomColor: color.primaryTextColor,
                  borderWidth: 1,
                  color: color.primaryTextColor,
                  textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
                }}
                placeholder=""
                textContentType="password"
                secureTextEntry={true}
                onChangeText={(text) => setpassword(text)}
              />
            </View>
            <TouchableOpacity
              onPress={() => {
                login();
              }}
              style={{ marginBottom: width / 15 }}
            >
              <View style={{ flexDirection: "row" }}>
                <Text
                  style={{
                    fontSize: width / 20,
                    color: color.primaryHighlightColor,
                    fontWeight: "bold",
                    textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
                  }}
                >
                  SIGN IN
                </Text>
              </View>
            </TouchableOpacity>
            <View style={{ flexDirection: "row", marginBottom: width / 15 }}>
              <Text
                style={{
                  fontSize: width / 35, color: color.primaryTextColor,
                  textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
                }}
              >
                Not registered yet?
              </Text>
              <TouchableOpacity
                onPress={() => {
                  navigation.push("SignUp", {
                    image: mobilelogokey,
                    color: color,
                  });
                }}
                style={{}}
              >
                <Text
                  style={{
                    fontSize: width / 35,
                    color: "blue",
                    // fontWeight: 'bold',
                    textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
                  }}
                >
                  {" "}
                  Create an Account
                </Text>
              </TouchableOpacity>
            </View>
            {/* <TouchableOpacity
              onPress={() => {
                userdetails();
              }}
              style={{marginBottom: width / 15}}>
              <View style={{flexDirection: 'row'}}>
                <Text
                  style={{
                    fontSize: width / 35,
                    color: 'blue',
                    fontWeight: 'bold',
                  }}>
                  Forgot your password
                </Text>
              </View>
            </TouchableOpacity> */}
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

export default LoginPage;
