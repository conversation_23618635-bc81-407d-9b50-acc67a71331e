import * as React from 'react';
import { SafeAreaView, ScrollView } from 'react-native';
import FullLandscapeList from '../components/FullLandscapeList';
import FullPortraitList from '../components/FullPortraitList';
import FullSquareList from '../components/FullSquareList';
import LandscapeListDes from '../components/LandscapeListDesc';

const SeeMoreScreen = ({ route }) => {
  const { itemId, listdatas, comp, color } = route.params;
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: color.primaryBackgroundColor }}>
      <ScrollView showsVerticalScrollIndicator={false} nestedScrollEnabled={true}>
        {comp == 'FullLandscapeList' ? (
          <FullLandscapeList color={color} listtitle={itemId} datas={listdatas} />
        ) : null}
        {comp == 'FullPortraitList' ? (
          <FullPortraitList color={color} listtitle={itemId} datas={listdatas} />
        ) : null}
        {comp == 'FullSquareList' ? (
          <FullSquareList color={color} listtitle={itemId} datas={listdatas} />
        ) : null}
        {comp == 'LandscapeListDec' ? (
          <LandscapeListDes color={color} listtitle={itemId} datas={listdatas} />
        ) : null}
      </ScrollView>
    </SafeAreaView>
  );
};
export default SeeMoreScreen;
