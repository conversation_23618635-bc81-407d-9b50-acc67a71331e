import * as React from "react";
import {
  Text,
  Dimensions,
  View,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  DevSettings,
} from "react-native";
import { useEffect, useState } from "react";
import { Fragment } from "react";
import ToggleSwitch from "toggle-switch-react-native";
import RNRestart from "react-native-restart";
import { useSelector } from "react-redux";
import { RootState } from "../redux/store";

const { width } = Dimensions.get("window");

const AppSettings = () => {
  const color = useSelector((state: RootState) => state.homeScreen.colors);
  const isRTL = useSelector((state: RootState) => state.homeScreen.isRTL);
  //   const [JwData, setJwData] = useState([]);
  //   const [ListID, setListID] = useState();
  const [Dark, setDark] = useState(false);
  const startReload = () => RNRestart.Restart();
  const styles = StyleSheet.create({
    row: {
      flex: 1,
      justifyContent: "space-around",
    },
    inputWrap: {
      flex: 1,
      borderColor: color.primaryTextColor,
      borderWidth: 1,
      height: width / 10,
      borderLeftColor: "transparent",
      borderRightColor: "transparent",
    },
    text1: {
      color: color.primaryTextColor,
      fontSize: width / 25,
      fontWeight: "bold",
      textAlign: "center",
    },
    text2: {
      color: color.primaryTextColor,
      fontSize: width / 30,
      fontWeight: "bold",
      textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
      right: width / 40,
      marginTop: width / 40,
    },
    text3: {
      color: color.primaryTextColor,
      fontSize: width / 30,
      fontWeight: "bold",
      textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
      left: width / 40,
      marginTop: width / 40,
    },
  });

  useEffect(() => {
    getItemValue();
  }, []);

  async function setItemValue(key, value) {
    try {
      console.log("hagwesgfw", value);
      AsyncStorage.setItem(key, value);
      value === "true" ? setDark(true) : setDark(false);
      startReload;
      DevSettings.reload();
    } catch (error) {
      // Error saving data
    }
  }

  async function getItemValue() {
    try {
      const value = await AsyncStorage.getItem("darkmode");
      value === "true" ? setDark(true) : setDark(false);
    } catch (exception) { }
  }

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <Fragment>
        <ScrollView
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled={true}
        >
          <View style={{ marginBottom: width / 25, marginTop: width / 25, direction: isRTL ? 'rtl' : 'ltr' }}>
            <Text style={styles.text1}>Settings</Text>
            <TouchableOpacity
              style={{ flexDirection: "row", marginTop: width / 20 }}
            >
              {/* <View style={styles.inputWrap}>
                <Text style={styles.text3}>App Appearance</Text>
              </View> */}
            </TouchableOpacity>
            <View style={{ flexDirection: "row", marginTop: -1 }}>
              <View style={styles.inputWrap}>
                <Text style={styles.text3}>Dark Mode</Text>
              </View>
              <View style={styles.inputWrap}>
                <View style={{ left: width / 2.8, marginTop: width / 40 }}>
                  <ToggleSwitch
                    isOn={Dark}
                    onColor="green"
                    offColor="red"
                    label=""
                    labelStyle={{ color: "black", fontWeight: "100" }}
                    size="small"
                    onToggle={(isOn) =>
                      setItemValue("darkmode", JSON.stringify(isOn))
                    }
                  />
                </View>
              </View>
            </View>
          </View>
        </ScrollView>
      </Fragment>
    </SafeAreaView>
  );
};

export default AppSettings;
