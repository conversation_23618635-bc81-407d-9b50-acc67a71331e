import * as React from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';
import { WebView } from 'react-native-webview';
import { useEffect, useState } from "react";
import { AppService } from '../services/AppService';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../redux/store';
import extractYouTubeVideoId from '../utils/helper';
import { store } from '../redux/store';

const LiveStreamScreen = () => {
    const { livePage } = useSelector(
        (state: RootState) => state.liveStreamScreen);
    const [isDataLoading, setIsDataLoading] = useState<boolean>(false);
    const [videoId, setVideoId] = useState<string>('');
    const [url, setUrl] = useState<string>('');
    const dispatch = useDispatch<AppDispatch>();

    const fetchLiveStreamData = async () => {
        setIsDataLoading(true);
        await AppService.fetchLiveStreamData(dispatch);
        setIsDataLoading(false);
        console.log("store.getState().liveStreamScreen.livePage----->", livePage);
        console.log("livePage----->", livePage);

        const extractedVideoId = extractYouTubeVideoId(store.getState().liveStreamScreen.livePage) ?? "";
        setVideoId(extractedVideoId);
        console.log(extractedVideoId);
        const embedUrl = `https://www.youtube.com/embed/${extractedVideoId}?autoplay=1&playsinline=1&controls=1`;
        setUrl(embedUrl)
    };

    useEffect(() => {
        fetchLiveStreamData();
    }, [livePage])


    return (
        isDataLoading ? (
            <View
                style={{ flex: 1, justifyContent: "center", alignItems: "center", backgroundColor: 'white' }}
            >
                <ActivityIndicator size="large" color='black' />
            </View>
        ) : (
            <View style={{ flex: 1 }}>
                <WebView

                    style={styles.webView}
                    allowsFullscreenVideo
                    allowsInlineMediaPlayback
                    mediaPlaybackRequiresUserAction={false}
                    javaScriptEnabled={true}
                    domStorageEnabled={true}
                    mixedContentMode='always'
                    source={{
                        uri: url
                    }}
                />
            </View>
        )
    );
};

const styles = StyleSheet.create({
    webView: {
        flex: 1,
    },
});

export default LiveStreamScreen;

