import * as React from 'react';
import {
  Text,
  Dimensions,
  View,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableHighlight,
  TouchableOpacity,
  Share,
} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/Ionicons';
import PortraitList from '../components/PortraitList';
import RoundList from '../components/RoundList';
import DropdownComponent from '../components/DropDownComponent';
import { ActivityIndicator } from 'react-native';
import { Image } from 'react-native-elements';
import { useState, useEffect } from 'react';
import axios from 'axios';
import { useFocusEffect } from '@react-navigation/native';
import { AppService } from '../services/AppService';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';

const { width } = Dimensions.get('window');

const SeasonScreen = ({ route }) => {
  const { item } = route.params;
  const color = useSelector((state: RootState) => state.homeScreen.colors);
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);
  const [Recommend, setRecommend] = useState([]);
  const [SelectedMenu, setSelectedMenu] = useState(1);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);


  const styles = StyleSheet.create({
    watchnowbuttoncontainer: {
      flexDirection: 'row',
      backgroundColor: color.primaryHighlightColor,
      alignItems: 'center',
      justifyContent: 'center',
      marginLeft: width / 22,
      marginRight: width / 22,
      marginTop: 0,
      marginBottom: width / 40,
      width: width / 1.1,
      height: width / 12,
      borderRadius: 5,
    },
    watchbuttoncontainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginLeft: width / 22,
      marginRight: width / -44,
      marginTop: 0,
      marginBottom: width / 40,
      width: width / 2 / 1.13,
      height: width / 12,
      borderRadius: 5,
      borderWidth: 0,
      borderColor: color.primaryTextColor,
    },
    Episodesbutton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginLeft: width / 22,
      marginRight: width / -44,
      marginTop: 0,
      marginBottom: width / 40,
      width: width / 3.3,
      height: width / 12,
      borderWidth: SelectedMenu == 1 ? 3 : 0,
      borderColor: 'transparent',
      borderBottomColor: color.primaryHighlightColor,
      borderRadius: 0,
      // backgroundColor: SelectedMenu == 1 ? color.primaryHighlightColor : null,
    },
    Relatedbutton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginLeft: width / 40,
      marginRight: width / -44,
      marginTop: 0,
      marginBottom: width / 40,
      width: width / 3.3,
      height: width / 12,
      borderWidth: SelectedMenu == 2 ? 3 : 0,
      borderColor: 'transparent',
      borderBottomColor: color.primaryHighlightColor,
      borderRadius: 0,
      // backgroundColor: SelectedMenu == 1 ? color.primaryHighlightColor : null,
    },
    Detailsbutton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginLeft: width / 40,
      marginRight: width / -44,
      marginTop: 0,
      marginBottom: width / 40,
      width: width / 3.3,
      height: width / 12,
      borderWidth: SelectedMenu == 3 ? 3 : 0,
      borderColor: 'transparent',
      borderBottomColor: color.primaryHighlightColor,
      borderRadius: 0,
      // backgroundColor: SelectedMenu == 1 ? color.primaryHighlightColor : null,
    },
    subbutton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginLeft: width / 22,
      marginRight: width / -44,
      marginTop: 0,
      marginBottom: width / 40,
      width: width / 2.25,
      height: width / 12,
      borderRadius: 5,
      borderWidth: 1,
      borderColor: color.primaryTextColor,
    },
  });

  const getdata = async () => {
    const token = await AppService.getToken();
    try {
      const result = await axios.get(
        AppService.baseUrl + '/api/view/' + AppService.appId + '/' +
        item.itemid._id +
        '/getPageFullItem',
        {
          headers: { Authorization: token },
        }
      );
      console.log('sddwsadsqwdcvh', result.data);
      setRecommend(result.data);
    } catch (e) {
      console.log(e);
      throw e;
    }
  };

  // useEffect(() => {
  //   Recommend.length == 0 ? getdata() : null;
  // }, [Recommend.length, getdata]);

  useFocusEffect(() => {
    Recommend.length == 0 ? getdata() : null;
  }, [Recommend.length, getdata]);

  const onShare = async () => {
    try {
      const result = await Share.share({
        message:
          'http://smottapp.com/share/index.php?type=season&title=' +
          item.itemid.displayName +
          '&appid=' + AppService.appId + '&id=' +
          item.itemid._id +
          '&image=' +
          imageUrl +
          item.itemid.landscapeThumbnail.imagekey +
          '&url=http://smottapp.com:3002/' + AppService.appId + '/' +
          item.itemid.urlName,
      });
      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // shared with activity type of result.activityType
        } else {
          // shared
        }
      } else if (result.action === Share.dismissedAction) {
        // dismissed
      }
    } catch (error) {
      alert(error.message);
    }
  };

  function secondsToHms(d) {
    d = Number(d);
    const h = Math.floor(d / 3600);
    const m = Math.floor((d % 3600) / 60);
    const s = Math.floor((d % 3600) % 60);

    const hDisplay = h > 0 ? h + (h == 1 ? ' Hr ' : ' Hrs ') : '';
    const mDisplay = m > 0 ? m + (m == 1 ? ' Min ' : ' Mins ') : '';
    const sDisplay = s > 0 ? s + (s == 1 ? ' Sec' : ' Sec') : '';
    return hDisplay + mDisplay + sDisplay;
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: color.primaryBackgroundColor }}>
      <ScrollView showsVerticalScrollIndicator={false} nestedScrollEnabled={true}>
        <View>
          <Image
            PlaceholderContent={<ActivityIndicator />}
            source={{
              uri: imageUrl + item.itemid.landscapeThumbnail.imagekey,
            }}
            style={{
              width: width,
              height: width / 1.8,
            }}
          />
        </View>
        {/* <View>
          <Text
            style={{
              color: color.primaryTextColor,
              margin: width / 22,
              fontWeight: 'bold',
              fontSize: width / 20,
              textAlign: isRTL ? 'right' : 'left',
              writingDirection: isRTL ? 'rtl' : 'ltr',
              marginTop: width / 25,
              marginBottom: width / 25,
            }}>
            {item.itemid.displayName}
          </Text>
        </View> */}

        <Text
          style={{
            color: color.primaryTextColor,
            margin: width / 22,
            fontWeight: 'bold',
            fontSize: width / 20,
            textAlign: isRTL ? 'right' : 'left',
            writingDirection: isRTL ? 'rtl' : 'ltr',
            marginTop: width / 25,
            marginBottom: width / 40,
          }}>
          {item.itemid.displayName}
        </Text>
        <View
          style={{
            marginLeft: width / 22,
            marginRight: width / 22,
            marginTop: 0,
            width: width / 1.1,
            marginBottom: 0,
          }}>
          <View style={{ flexDirection: 'row' }}>
            {item.itemid.rating != '' && item.itemid.rating != null ? (
              <Text
                style={{
                  marginLeft: 0,
                  fontSize: width / 38,
                  textAlign: isRTL ? 'right' : 'left',
                  writingDirection: isRTL ? 'rtl' : 'ltr',
                  marginTop: 1,
                  marginBottom: width / 25,
                  color: '#808080',
                }}>
                Rating : {item.itemid.rating}
              </Text>
            ) : null}
            {item.itemid.VIdeoDuration != '' && item.itemid.VIdeoDuration != null ? (
              <Text
                style={{
                  marginLeft: width / 38,
                  fontSize: width / 38,
                  textAlign: isRTL ? 'right' : 'left',
                  writingDirection: isRTL ? 'rtl' : 'ltr',
                  marginTop: 1,
                  marginBottom: width / 25,
                  color: '#808080',
                }}>
                {secondsToHms(item.itemid.duration)}
              </Text>
            ) : null}
            {item.itemid.ReleaseOn != '' && item.itemid.ReleaseOn != null ? (
              <Text
                style={{
                  marginLeft: width / 38,
                  fontSize: width / 38,
                  textAlign: isRTL ? 'right' : 'left',
                  writingDirection: isRTL ? 'rtl' : 'ltr',
                  marginTop: 1,
                  marginBottom: width / 25,
                  color: '#808080',
                }}>
                {item.itemid.ReleaseOn}
              </Text>
            ) : null}
            {/* <Text
              style={{
                marginLeft: width / 38,
                fontSize: width / 38,
                textAlign: isRTL ? 'right' : 'left',
            writingDirection: isRTL ? 'rtl' : 'ltr',
                marginTop: 0,
                marginBottom: width / 25,
                color: '#808080',
                borderWidth: 1,
                borderColor: '#808080',
                borderRadius: 5,
              }}>
              X-Ray
            </Text> */}
            {/* <Text
              style={{
                marginLeft: width / 38,
                fontSize: width / 38,
                textAlign: isRTL ? 'right' : 'left',
            writingDirection: isRTL ? 'rtl' : 'ltr',
                marginTop: 0,
                marginBottom: width / 25,
                color: '#808080',
                borderWidth: 1,
                borderColor: '#808080',
                borderRadius: 5,
              }}>
              All
            </Text> */}
            {item.itemid.MPAARating != '' && item.itemid.MPAARating != null ? (
              <Text
                style={{
                  marginLeft: width / 38,
                  fontSize: width / 38,
                  textAlign: isRTL ? 'right' : 'left',
                  writingDirection: isRTL ? 'rtl' : 'ltr',
                  marginTop: 0,
                  marginBottom: width / 25,
                  color: '#808080',
                  borderWidth: 1,
                  borderColor: '#808080',
                  borderRadius: 5,
                }}>
                {item.itemid.MPAARating}
              </Text>
            ) : null}
          </View>
          {item.itemid.genre != '' && item.itemid.genre != null ? (
            <Text
              style={{
                marginLeft: 0,
                fontSize: width / 38,
                textAlign: isRTL ? 'right' : 'left',
                writingDirection: isRTL ? 'rtl' : 'ltr',
                marginTop: width / -30,
                marginBottom: width / 25,
                color: '#808080',
              }}>
              {item.itemid.genre}
            </Text>
          ) : null}
          {item.itemid.audio != '' && item.itemid.audio != null ? (
            <Text
              style={{
                marginLeft: 0,
                fontSize: width / 38,
                textAlign: isRTL ? 'right' : 'left',
                writingDirection: isRTL ? 'rtl' : 'ltr',
                marginTop: width / -30,
                marginBottom: width / 25,
                color: '#808080',
              }}>
              Audio : {item.itemid.audio}
            </Text>
          ) : null}
        </View>
        <TouchableOpacity
          onPress={() => {
            // navigation.push('FullScreen', {
            //   itemurl: JwDataDetails.MovieId,
            //   image: JwDataDetails.image,
            //   color: color,
            // });
          }}
          style={styles.watchnowbuttoncontainer}>
          <View style={{ flexDirection: 'row' }}>
            <MaterialCommunityIcons
              name="play"
              color={color.elementForegroundColor}
              size={width / 25}
            />
            <Text
              style={{
                fontSize: width / 30,
                color: color.elementForegroundColor,
                marginLeft: 5,
                textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
              }}>
              Watch Now
            </Text>
          </View>
        </TouchableOpacity>
        <View style={{ flexDirection: 'row' }}>
          <TouchableOpacity onPress={onShare} style={styles.subbutton}>
            <View style={{ flexDirection: 'row' }}>
              <MaterialCommunityIcons
                name="star"
                color={color.primaryHighlightColor}
                size={width / 26}
              />
              <Text
                style={{
                  fontSize: width / 30,
                  color: color.primaryHighlightColor,
                  marginLeft: 5,
                  textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
                }}>
                {isRTL ? 'المفضلة' : 'Favourite'}
              </Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity onPress={onShare} style={styles.subbutton}>
            <View style={{ flexDirection: 'row' }}>
              <MaterialCommunityIcons
                name="share"
                color={color.primaryTextColor}
                size={width / 25}
              />
              <Text
                style={{
                  fontSize: width / 30,
                  color: color.primaryTextColor,
                  marginLeft: 5,
                  textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
                }}>
                {isRTL ? 'مشاركة' : 'Share'}
              </Text>
            </View>
          </TouchableOpacity>
        </View>
        {SelectedMenu != 0 ? (
          <View style={{ flexDirection: 'row', marginTop: width / 30 }}>
            <TouchableOpacity
              onPress={() => {
                setSelectedMenu(1);
              }}
              style={styles.Episodesbutton}>
              <View style={{ flexDirection: 'row' }}>
                <Text
                  style={{
                    fontSize: width / 30,
                    color: color.primaryTextColor,
                    marginLeft: 5,
                    textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
                  }}>
                  {isRTL ? 'الحلقات' : 'Episodes'}

                </Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setSelectedMenu(3);
              }}
              style={styles.Detailsbutton}>
              <View style={{ flexDirection: 'row' }}>
                <Text
                  style={{
                    fontSize: width / 30,
                    color: color.primaryTextColor,
                    marginLeft: 5,
                    textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
                  }}>
                  {isRTL ? 'تفاصيل' : 'Details'}
                </Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setSelectedMenu(2);
              }}
              style={styles.Relatedbutton}>
              <View style={{ flexDirection: 'row' }}>
                <Text
                  style={{
                    fontSize: width / 30,
                    color: color.primaryTextColor,
                    marginLeft: 5,
                    textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
                  }}>
                  {isRTL ? 'ذو صلة' : 'Related'}

                </Text>
              </View>
            </TouchableOpacity>
          </View>
        ) : null}
        {SelectedMenu == 3 || SelectedMenu == 0 ? (
          <View>
            <Text
              style={{
                color: color.primaryTextColor,
                fontSize: width / 30,
                fontWeight: 'bold',
                left: width / 40,
                textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
              }}>
              {isRTL ? 'ملخص' : 'Synopsis'}

            </Text>
            <Text
              style={{
                margin: width / 22,
                fontSize: width / 32,
                textAlign: isRTL ? 'right' : 'left',
                writingDirection: isRTL ? 'rtl' : 'ltr',
                marginTop: width / 30,
                marginBottom: width / 25,
                color: color.primaryTextColor,
              }}>
              {item.itemid.description}
            </Text>
            <RoundList
              color={color}
              seemorecomp={''}
              listtitle={'CAST & CREW'}
              seemoretext={''}
              seemoretextvis={false}
              url={''}
              urltype={''}
              datas={item.itemid}
            />
          </View>
        ) : null}
        {SelectedMenu == 1 || SelectedMenu == 0 ? (
          <DropdownComponent
            color={color}
            seemorecomp={''}
            listtitle={'CAST & CREW'}
            seemoretext={''}
            seemoretextvis={false}
            url={''}
            urltype={''}
            data={item.itemid.items}
          />
        ) : null}
        {console.log('gdsgfahsg', Recommend)}
        {SelectedMenu == 2 || SelectedMenu == 0 ? (
          <PortraitList
            color={color}
            seemorecomp={''}
            listtitle={''}
            seemoretext={''}
            seemoretextvis={false}
            url={''}
            urltype={''}
            datas={Recommend.recommendation}
          />
        ) : null}
      </ScrollView>
    </SafeAreaView>
  );
};

export default SeasonScreen;
