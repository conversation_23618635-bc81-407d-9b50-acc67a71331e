import axios from 'axios';
import { AppService } from '../services/AppService';
import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { store } from './store';

export interface MainScreenState {
  car: [];
  isLoading: boolean;
  error: null | string;
  listCount: number;
  totalListCount: number;
  isBottomTabActive: boolean;
}

const initialState: MainScreenState = {
  car: [],
  isLoading: true,
  error: null,
  listCount: 5,
  totalListCount: 0,
  isBottomTabActive: true,
};

// Async thunk for fetching data
export const fetchData = createAsyncThunk(
  'home/fetchData',
  async ({ urlname, listCount }, { rejectWithValue }) => {
    console.log('fetchData =>', urlname);
    console.log('fetchData AppService.appId=>', store.getState().authScreen.appId);
    try {
      const token = await AppService.getToken();
      console.log('fetchData =>', urlname, token);
      const response = await axios.get(
        `${AppService.baseUrl}/api/view/${urlname}/${AppService.appId}/getUrlFullItem`,
        {
          params: { limit: listCount, skip: 0 },
          headers: { Authorization: token },
        }
      );
      console.log('fetchData response =>', response.data);
      return response.data;
    } catch (error) {
      console.log('fetchData Error =>', error);
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

const mainScreenSlice = createSlice({
  name: 'mainScreen',
  initialState,
  reducers: {
    incrementListCount: (state) => {
      state.listCount += 5;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setActiveTabState: (state, action: PayloadAction<boolean>) => {
      state.isBottomTabActive = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder.addCase(fetchData.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchData.fulfilled, (state, action) => {
      console.log('fetchData.fulfilled =>', action.payload.items);
      state.car = [...action.payload.items];
      state.totalListCount = action.payload.itemCount ?? 0;
      state.isLoading = false;
      console.log('fetchData.fulfilled state =>', state.car);
    });
    builder.addCase(fetchData.rejected, (state, action: PayloadAction<string | null>) => {
      state.isLoading = false;
      state.error = action.payload ?? 'An Error Occurred';
    });
  },
});

export const selectIsLoading = (state) => state.isLoading;
export const { incrementListCount, setLoading, setActiveTabState } = mainScreenSlice.actions;
export default mainScreenSlice.reducer;
