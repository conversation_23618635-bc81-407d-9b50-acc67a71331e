import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export interface AuthState {
  appId: string;
}

const initialState: AuthState = {
  appId: "",
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    updateAppID: (state, action: PayloadAction<string>) => {
      state.appId = action.payload;
    },
  },
});

// Exporting actions and reducer
export const { updateAppID } = authSlice.actions;

export default authSlice.reducer;
