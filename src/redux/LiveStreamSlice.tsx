import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export interface LiveStreamState {
    _id: string;
    RFP: boolean;
    pageType: string;
    displayName: string;
    urlName: string;
    // portraitThumbnail: BannerImage;
    // landscapeThumbnail: BannerImage;
    // bannerImage: BannerImage;
    genre: null;
    starring: null;
    rating: null;
    ageRating: null;
    description: null;
    author: null;
    publishDate: null;
    publishedDate: null;
    mainHeader: null;
    externalPage: null;
    livePage: string;
    internalPage: null;
    blockQuote: null;
    sectionHeader1: null;
    sectionHeader2: null;
    introduction: null;
    sectionDescription1: null;
    sectionDescription2: null;
    contentType: null;
    premiumType: null;
    videoId: null;
    audio: null;
    tag: null;
    firstTag: null;
    secondTag: null;
    director: null;
    writer: null;
    recommendation: null;
    cite: null;
    studio: null;
    producer: null;
    application: string;
    devices: Device[];
    items: any[];
    languages: null;
    draftStatus: boolean;
    publishStatus: boolean;
    republishStatus: boolean;
    createdBy: string;
    // date: Date;
    // createdDate: Date;
    __v: number;
    itemCount: number;
}

export interface BannerImage {
    _id: string;
    imageName: string;
    application: string;
    imagekey: string;
    createdBy: string;
    date: Date;
    __v: number;
}

export interface Device {
    _id: string;
    itemName: string;
    itemSize: string;
    itemVisibility: boolean;
}



const initialState: LiveStreamState = {
    _id: "",
    RFP: false,
    pageType: "",
    displayName: "",
    urlName: "",
    // portraitThumbnail: {
    //     _id: "",
    //     imageName: "",
    //     application: "",
    //     imagekey: "",
    //     createdBy: "",
    //     date: new Date(),
    //     __v: 0,
    // },
    // landscapeThumbnail: {
    //     _id: "",
    //     imageName: "",
    //     application: "",
    //     imagekey: "",
    //     createdBy: "",
    //     date: new Date(),
    //     __v: 0,
    // },
    // bannerImage: {
    //     _id: "",
    //     imageName: "",
    //     application: "",
    //     imagekey: "",
    //     createdBy: "",
    //     date: new Date(),
    //     __v: 0,
    // },
    genre: null,
    starring: null,
    rating: null,
    ageRating: null,
    description: null,
    author: null,
    publishDate: null,
    publishedDate: null,
    mainHeader: null,
    externalPage: null,
    livePage: "",
    internalPage: null,
    blockQuote: null,
    sectionHeader1: null,
    sectionHeader2: null,
    introduction: null,
    sectionDescription1: null,
    sectionDescription2: null,
    contentType: null,
    premiumType: null,
    videoId: null,
    audio: null,
    tag: null,
    firstTag: null,
    secondTag: null,
    director: null,
    writer: null,
    recommendation: null,
    cite: null,
    studio: null,
    producer: null,
    application: "",
    devices: [],
    items: [],
    languages: null,
    draftStatus: false,
    publishStatus: false,
    republishStatus: false,
    createdBy: "",
    // date: new Date(),
    // createdDate: new Date(),
    __v: 0,
    itemCount: 0
};

const liveStreamSlice = createSlice({
    name: "liveStream",
    initialState,
    reducers: {
        updateLivePageUrl: (state, action: PayloadAction<string>) => {
            state.livePage = action.payload;
        }
    },
});

// Exporting actions and reducer
export const { updateLivePageUrl } = liveStreamSlice.actions;

export default liveStreamSlice.reducer;
