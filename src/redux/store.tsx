// redux/store.js

import { configureStore } from "@reduxjs/toolkit";
import homeScreenReducer from "./HomeScreenSlice";
import mainScreenReducer from "./MainScreenSlice";
import authReducer from "./AuthSlice";
import liveStreamReducer from "./LiveStreamSlice";

// Define the root state type
export interface RootState {
  homeScreen: ReturnType<typeof homeScreenReducer>;
  mainScreen: ReturnType<typeof mainScreenReducer>;
  authScreen: ReturnType<typeof authReducer>;
  liveStreamScreen: ReturnType<typeof liveStreamReducer>;
}

// Configure the store with the counter reducer
export const store = configureStore({
  reducer: {
    homeScreen: homeScreenReducer,
    mainScreen: mainScreenReducer,
    authScreen: authReducer,
    liveStreamScreen: liveStreamReducer,
  },
});

export type AppDispatch = typeof store.dispatch;
export type AppSelector = (state: RootState) => RootState;
